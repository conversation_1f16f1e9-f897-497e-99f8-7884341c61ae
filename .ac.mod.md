# ac_mod_generator

一个独立的功能组件，用于自动生成和维护项目中的 `.ac.mod.md` 模块文档文件。

## 目录结构

```
└── ac_mod_generator/                 # 子目录
    ├── tests                 # 测试目录
    │   ├── __init__.py
    │   ├── test_generator.py
    │   ├── test_module_scanner.py
    │   └── test_template_engine.py
    ├── 1.md                 # MD 文件
    ├── CONTEXT_LIMITING_SUMMARY.md                 # MD 文件
    ├── HIERARCHICAL_GENERATION_SUMMARY.md                 # MD 文件
    ├── JAVA_SUPPORT.md                 # MD 文件
    ├── README.md                 # 项目说明文档
    ├── __init__.py
    ├── cli.py
    ├── context_limiter.py
    ├── demo_template_generation.py
    ├── example_usage.py
    ├── file_utils.py
    ├── generator.py
    ├── hierarchical_example.py
    ├── java_context_example.py
    ├── java_example.py
    ├── llm_config.py
    ├── module_scanner.py
    ├── template_engine.py
    ├── test_basic_functionality.py
    ├── test_context_limiting.py
    ├── test_fallback_mechanism.py
    ├── test_integration.py
    ├── test_java_support.py
    └── test_llm_integration.py
```

## 快速开始

### 基本使用方式

```python
#!/usr/bin/env python3
"""
Example usage of AC Module Documentation Generator

This script demonstrates various ways to use the AC Module Generator
for generating and maintaining .ac.mod.md files.
"""

import logging
from pathlib import Path
from autocoder.ac_mod_generator import ACModGenerator, ModuleScanner, TemplateEngine, FileUtils


def setup_logging():
    """Setup logging for the example."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def example_basic_usage():
    """Example 1: Basic usage - generate documentation for a single module."""
    print("=== Example 1: Basic Usage ===")
    
    # Initialize generator with current directory as root
    generator = ACModGenerator(root_path=".")
    
    # Generate documentation for this module itself
    module_path = Path("src/autocoder/ac_mod_generator")
    
    if module_path.exists():
        print(f"Generating documentation for: {module_path}")
        success = generator.generate_documentation(module_path, force=True)
        
        if success:
            print("✓ Documentation generated successfully!")
            doc_path = module_path / ".ac.mod.md"
            print(f"  Documentation saved to: {doc_path}")
        else:
            print("✗ Failed to generate documentation")
    else:
        print(f"Module path does not exist: {module_path}")


def example_discover_modules():
    """Example 2: Discover all potential modules in the project."""
    print("\n=== Example 2: Module Discovery ===")
    
    generator = ACModGenerator(root_path="src")
    
    print("Discovering potential modules...")
    modules = generator.discover_modules()
    
    print(f"Found {len(modules)} potential modules:")
    for i, module in enumerate(modules, 1):
        rel_path = module.relative_to(Path("src")) if module.is_relative_to(Path("src")) else module
        doc_exists = (module / ".ac.mod.md").exists()
        status = "📄" if doc_exists else "❌"
        print(f"  {i:2d}. {status} {rel_path}")
    
    print("\n📄 = has .ac.mod.md, ❌ = missing .ac.mod.md")


def example_batch_generation():
    """Example 3: Batch generate documentation for all modules."""
    print("\n=== Example 3: Batch Generation ===")
    
    generator = ACModGenerator(root_path="src")
    
    print("Generating documentation for all discovered modules...")
    results = generator.generate_all_documentation(force=False)
    
    success_count = sum(1 for success in results.values() if success)
    total_count = len(results)
    
    print(f"\nGeneration complete: {success_count}/{total_count} successful")
    
    for module_path, success in results.items():
        status = "✓" if success else "✗"
        rel_path = Path(module_path).relative_to(Path("src")) if Path(module_path).is_relative_to(Path("src")) else module_path
        print(f"  {status} {rel_path}")


def example_custom_template():
    """Example 4: Using a custom template."""
    print("\n=== Example 4: Custom Template ===")
    
    # Create a simple custom template
    custom_template = """# {{MODULE_NAME}} - Custom Template

{{MODULE_DESCRIPTION}}

## 📁 Directory Structure
{{DIRECTORY_STRUCTURE}}

## 🔧 Core Components
{{CORE_COMPONENTS}}

## 📖 Usage Examples
{{USAGE_EXAMPLES}}

## 🔗 Dependencies
- /home/<USER>/fengbin/DailyWork/ZeroAgents/业务语义/ac_mod_generator_v1/.ac.mod.md

## 🧪 Testing
```bash
pytest ac_mod_generator -v
```

---
*Generated with custom template*
"""
    
    # Save custom template
    template_path = Path("custom_template.md")
    template_path.write_text(custom_template)
    
    try:
        # Use custom template
        generator = ACModGenerator(root_path=".", template_path=str(template_path))
        
        # Generate documentation with custom template
        module_path = Path("src/autocoder/ac_mod_generator")
        if module_path.exists():
            print(f"Generating documentation with custom template...")
            success = generator.generate_documentation(module_path, force=True)
            
            if success:
                print("✓ Custom template documentation generated!")
                doc_path = module_path / ".ac.mod.md"
                print(f"  Check the custom formatting in: {doc_path}")
            else:
                print("✗ Failed to generate documentation with custom template")
    
    finally:
        # Clean up custom template file
        if template_path.exists():
            template_path.unlink()
            print(f"  Cleaned up custom template: {template_path}")


def example_update_existing():
    """Example 5: Update existing documentation."""
    print("\n=== Example 5: Update Existing Documentation ===")
    
    generator = ACModGenerator(root_path=".")
    
    module_path = Path("src/autocoder/ac_mod_generator")
    doc_path = module_path / ".ac.mod.md"
    
    if doc_path.exists():
        print(f"Updating existing documentation: {doc_path}")
        
        # Read original content
        original_size = doc_path.stat().st_size
        
        success = generator.update_existing_documentation(module_path)
        
        if success:
            new_size = doc_path.stat().st_size
            print(f"✓ Documentation updated successfully!")
            print(f"  Size changed: {original_size} → {new_size} bytes")
        else:
            print("✗ Failed to update documentation")
    else:
        print(f"No existing documentation found at: {doc_path}")


def example_component_analysis():
    """Example 6: Detailed component analysis."""
    print("\n=== Example 6: Component Analysis ===")
    
    scanner = ModuleScanner(root_path=".")
    module_path = Path("src/autocoder/ac_mod_generator")
    
    if module_path.exists():
        print(f"Analyzing module: {module_path}")
        
        # Get directory structure
        print("\n📁 Directory Structure:")
        structure = scanner.get_directory_structure(module_path)
        print(f"  Root: {structure['name']}")
        for child in structure.get('children', [])[:5]:  # Show first 5 items
            print(f"    - {child['name']}")
        if len(structure.get('children', [])) > 5:
            print(f"    ... and {len(structure['children']) - 5} more items")
        
        # Get core components
        print("\n🔧 Core Components:")
        components = scanner.extract_core_components(module_path)
        for i, component in enumerate(components[:3], 1):  # Show first 3 components
            print(f"  {i}. {component['name']} ({component['type']})")
            if component.get('description'):
                print(f"     {component['description'][:60]}...")
        if len(components) > 3:
            print(f"  ... and {len(components) - 3} more components")
        
        # Get dependencies
        print("\n🔗 Dependencies:")
        dependencies = scanner.find_dependencies(module_path)
        if dependencies:
            for dep in dependencies[:3]:  # Show first 3 dependencies
                print(f"  - {dep}")
            if len(dependencies) > 3:
                print(f"  ... and {len(dependencies) - 3} more dependencies")
        else:
            print("  No dependencies found")
        
        # Get test commands
        print("\n🧪 Test Commands:")
        test_commands = scanner.find_test_commands(module_path)
        for cmd in test_commands:
            print(f"  - {cmd}")


def main():
    """Run all examples."""
    setup_logging()
    
    print("AC Module Documentation Generator - Usage Examples")
    print("=" * 55)
    
    try:
        example_basic_usage()
        example_discover_modules()
        example_batch_generation()
        example_custom_template()
        example_update_existing()
        example_component_analysis()
        
        print("\n" + "=" * 55)
        print("All examples completed successfully!")
        print("\nNext steps:")
        print("1. Check the generated .ac.mod.md files")
        print("2. Try the command line interface:")
        print("   python -m autocoder.ac_mod_generator.cli --help")
        print("3. Run the tests:")
        print("   pytest src/autocoder/ac_mod_generator/tests/ -v")
        
    except Exception as e:
        print(f"\n❌ Error running examples: {e}")
        logging.exception("Error in example execution")


if __name__ == "__main__":
    main()

```

```python
#!/usr/bin/env python3
"""
模板生成演示脚本

展示模板生成的完整过程，包括数据收集、模板渲染和最终输出。
"""

import tempfile
from pathlib import Path
from generator import ACModGenerator
from template_engine import TemplateEngine
from module_scanner import ModuleScanner


def create_demo_module():
    """创建一个演示模块"""
    temp_dir = tempfile.mkdtemp()
    demo_module = Path(temp_dir) / "demo_module"
    demo_module.mkdir()
    
    # 创建 __init__.py
    (demo_module / "__init__.py").write_text('''"""
Demo Module for Template Generation

这是一个演示模块，用于展示模板生成的工作原理。
"""

__version__ = "1.0.0"
__author__ = "AC Module Generator"

from .core import DemoClass
from .utils import helper_function

__all__ = ["DemoClass", "helper_function"]
''')
    
    # 创建核心类文件
    (demo_module / "core.py").write_text('''"""
Core functionality of the demo module
"""

class DemoClass:
    """
    Main demonstration class.
    
    This class shows how the template generator analyzes
    and documents Python classes.
    """
    
    def __init__(self, name: str):
        """Initialize the demo class."""
        self.name = name
    
    def greet(self) -> str:
        """Return a greeting message."""
        return f"Hello from {self.name}!"
    
    def process_data(self, data: list) -> dict:
        """
        Process input data and return results.
        
        Args:
            data: List of items to process
            
        Returns:
            Dictionary with processing results
        """
        return {
            "processed": len(data),
            "items": data
        }
''')
    
    # 创建工具函数文件
    (demo_module / "utils.py").write_text('''"""
Utility functions for the demo module
"""

def helper_function(value: str) -> str:
    """
    A helper function that processes strings.
    
    Args:
        value: Input string to process
        
    Returns:
        Processed string
    """
    return value.upper().strip()

def calculate_score(items: list) -> float:
    """Calculate average score from a list of numbers."""
    if not items:
        return 0.0
    return sum(items) / len(items)
''')
    
    # 创建示例文件
    (demo_module / "example.py").write_text('''#!/usr/bin/env python3
"""
Usage example for the demo module
"""

from demo_module import DemoClass, helper_function

def main():
    # 1. 创建实例
    demo = DemoClass("Demo")
    
    # 2. 基本使用
    message = demo.greet()
    print(message)
    
    # 3. 处理数据
    data = [1, 2, 3, 4, 5]
    result = demo.process_data(data)
    print(f"处理结果: {result}")
    
    # 4. 使用工具函数
    processed = helper_function("  hello world  ")
    print(f"处理后的字符串: {processed}")

if __name__ == "__main__":
    main()
''')
    
    # 创建测试文件
    (demo_module / "test_demo.py").write_text('''"""
Tests for the demo module
"""

import unittest
from demo_module import DemoClass, helper_function

class TestDemoModule(unittest.TestCase):
    
    def test_demo_class(self):
        demo = DemoClass("Test")
        self.assertEqual(demo.greet(), "Hello from Test!")
    
    def test_helper_function(self):
        result = helper_function("  test  ")
        self.assertEqual(result, "TEST")

if __name__ == "__main__":
    unittest.main()
''')
    
    return demo_module


def demonstrate_data_collection(module_path: Path):
    """演示数据收集过程"""
    print("=" * 60)
    print("1. 数据收集阶段")
    print("=" * 60)
    
    scanner = ModuleScanner(module_path.parent)
    
    # 收集基本信息
    print(f"模块名称: {module_path.name}")
    print(f"模块路径: {module_path}")
    
    # 分析目录结构
    print("\n目录结构分析:")
    structure = scanner.get_directory_structure(module_path)
    print(f"  发现文件: {len(structure.get('children', []))} 个")
    
    # 分析核心组件
    print("\n核心组件分析:")
    components = scanner.extract_core_components(module_path)
    print(f"  发现组件: {len(components)} 个")
    for comp in components:
        print(f"    - {comp.get('name')} ({comp.get('type')})")
    
    # 查找使用示例
    print("\n使用示例分析:")
    examples = scanner.find_usage_examples(module_path)
    print(f"  发现示例: {len(examples)} 个")
    
    # 查找测试命令
    print("\n测试命令分析:")
    test_commands = scanner.find_test_commands(module_path)
    print(f"  发现测试命令: {len(test_commands)} 个")
    
    return {
        "name": module_path.name,
        "path": str(module_path.relative_to(module_path.parent)),
        "description": scanner.extract_module_description(module_path),
        "directory_structure": structure,
        "core_components": components,
        "dependencies": scanner.find_dependencies(module_path),
        "usage_examples": examples,
        "test_commands": test_commands
    }


def demonstrate_template_rendering(analysis_data: dict):
    """演示模板渲染过程"""
    print("\n" + "=" * 60)
    print("2. 模板渲染阶段")
    print("=" * 60)
    
    engine = TemplateEngine()
    
    print("模板变量替换:")
    print(f"  {{{{MODULE_NAME}}}} -> {analysis_data.get('name')}")
    print(f"  {{{{MODULE_DESCRIPTION}}}} -> {analysis_data.get('description', '模块描述')[:50]}...")
    
    print("\n复杂部分渲染:")
    
    # 渲染目录结构
    structure = engine._render_directory_structure(analysis_data.get("directory_structure", {}))
    print(f"  目录结构: {len(structure.split(chr(10)))} 行")
    
    # 渲染核心组件
    components = engine._render_core_components(analysis_data.get("core_components", []))
    print(f"  核心组件: {len(components.split(chr(10)))} 行")
    
    # 渲染使用示例
    examples = engine._render_usage_examples(analysis_data.get("usage_examples", []))
    print(f"  使用示例: {len(examples.split(chr(10)))} 行")
    
    # 完整渲染
    print("\n执行完整模板渲染...")
    content = engine.render(analysis_data)
    
    return content


def demonstrate_final_output(content: str, module_path: Path):
    """演示最终输出"""
    print("\n" + "=" * 60)
    print("3. 最终输出")
    print("=" * 60)
    
    print(f"生成的文档长度: {len(content)} 字符")
    print(f"生成的文档行数: {len(content.split(chr(10)))} 行")
    
    # 保存到文件
    doc_path = module_path / ".ac.mod.md"
    doc_path.write_text(content, encoding='utf-8')
    print(f"文档已保存到: {doc_path}")
    
    # 显示文档预览
    print("\n文档预览 (前20行):")
    print("-" * 40)
    lines = content.split('\n')
    for i, line in enumerate(lines[:20], 1):
        print(f"{i:2d}: {line}")
    if len(lines) > 20:
        print(f"... (还有 {len(lines) - 20} 行)")
    
    return doc_path


def main():
    """主演示函数"""
    print("模板生成完整流程演示")
    print("=" * 60)
    
    # 创建演示模块
    print("创建演示模块...")
    demo_module = create_demo_module()
    print(f"演示模块创建完成: {demo_module}")
    
    try:
        # 1. 数据收集
        analysis_data = demonstrate_data_collection(demo_module)
        
        # 2. 模板渲染
        content = demonstrate_template_rendering(analysis_data)
        
        # 3. 最终输出
        doc_path = demonstrate_final_output(content, demo_module)
        
        print("\n" + "=" * 60)
        print("演示完成！")
        print("=" * 60)
        print(f"生成的文档位于: {doc_path}")
        print("\n模板生成的关键步骤:")
        print("1. 模块扫描 - 分析代码结构和组件")
        print("2. 数据提取 - 收集类、函数、文档字符串等")
        print("3. 模板加载 - 使用默认或自定义模板")
        print("4. 变量替换 - 将分析数据填入模板")
        print("5. 内容渲染 - 生成最终的Markdown文档")
        print("6. 文件输出 - 保存为.ac.mod.md文件")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

```

```python
#!/usr/bin/env python3
"""
Hierarchical Documentation Generation Example

This example demonstrates the new hierarchical documentation generation feature
for large Java modules.
"""

import tempfile
import shutil
from pathlib import Path
from generator import ACModGenerator


def create_sample_java_project():
    """Create a sample Java project structure for demonstration."""
    # Create temporary directory
    temp_dir = Path(tempfile.mkdtemp())
    print(f"Creating sample Java project in: {temp_dir}")
    
    # Create main Java module
    java_module = temp_dir / "large-java-module"
    java_module.mkdir()
    (java_module / "pom.xml").write_text("""<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.example</groupId>
    <artifactId>large-java-module</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>
    <name>Large Java Module</name>
</project>""")
    
    # Create standard Maven structure
    src_main_java = java_module / "src" / "main" / "java" / "com" / "example" / "app"
    src_main_java.mkdir(parents=True)
    
    # Create main application class
    (src_main_java / "Application.java").write_text("""package com.example.app;

/**
 * Main application class for the large Java module.
 */
public class Application {
    public static void main(String[] args) {
        System.out.println("Large Java Module Application");
    }
}""")
    
    # Create controller package
    controller_pkg = src_main_java / "controller"
    controller_pkg.mkdir()
    
    (controller_pkg / "UserController.java").write_text("""package com.example.app.controller;

/**
 * REST controller for user management operations.
 */
public class UserController {
    public String getUser(Long id) {
        return "User " + id;
    }
    
    public String createUser(String name) {
        return "Created user: " + name;
    }
}""")
    
    (controller_pkg / "OrderController.java").write_text("""package com.example.app.controller;

/**
 * REST controller for order management operations.
 */
public class OrderController {
    public String getOrder(Long id) {
        return "Order " + id;
    }
    
    public String createOrder(String details) {
        return "Created order: " + details;
    }
}""")
    
    (controller_pkg / "ProductController.java").write_text("""package com.example.app.controller;

/**
 * REST controller for product management operations.
 */
public class ProductController {
    public String getProduct(Long id) {
        return "Product " + id;
    }
    
    public String createProduct(String name, Double price) {
        return "Created product: " + name + " ($" + price + ")";
    }
}""")
    
    # Create service package
    service_pkg = src_main_java / "service"
    service_pkg.mkdir()
    
    (service_pkg / "UserService.java").write_text("""package com.example.app.service;

/**
 * Business logic service for user operations.
 */
public class UserService {
    public boolean validateUser(String username) {
        return username != null && !username.isEmpty();
    }
}""")
    
    (service_pkg / "OrderService.java").write_text("""package com.example.app.service;

/**
 * Business logic service for order operations.
 */
public class OrderService {
    public double calculateTotal(double amount, double tax) {
        return amount + (amount * tax);
    }
}""")
    
    # Create domain package
    domain_pkg = src_main_java / "domain"
    domain_pkg.mkdir()
    
    (domain_pkg / "User.java").write_text("""package com.example.app.domain;

/**
 * User domain entity.
 */
public class User {
    private Long id;
    private String name;
    private String email;
    
    // Getters and setters would be here
}""")
    
    (domain_pkg / "Order.java").write_text("""package com.example.app.domain;

/**
 * Order domain entity.
 */
public class Order {
    private Long id;
    private String details;
    private Double amount;
    
    // Getters and setters would be here
}""")
    
    (domain_pkg / "Product.java").write_text("""package com.example.app.domain;

/**
 * Product domain entity.
 */
public class Product {
    private Long id;
    private String name;
    private Double price;
    
    // Getters and setters would be here
}""")
    
    # Create config package
    config_pkg = src_main_java / "config"
    config_pkg.mkdir()
    
    (config_pkg / "DatabaseConfig.java").write_text("""package com.example.app.config;

/**
 * Database configuration class.
 */
public class DatabaseConfig {
    private String url;
    private String username;
    private String password;
    
    // Configuration methods would be here
}""")
    
    return temp_dir, java_module


def demonstrate_hierarchical_generation():
    """Demonstrate hierarchical documentation generation."""
    print("=== Hierarchical Documentation Generation Demo ===\n")
    
    # Create sample project
    temp_dir, java_module = create_sample_java_project()
    
    try:
        # Initialize generator
        generator = ACModGenerator(temp_dir)
        
        print("1. Standard module discovery (without subpackages):")
        standard_modules = generator.discover_modules(include_subpackages=False)
        for module in standard_modules:
            print(f"   - {module.relative_to(temp_dir)}")
        
        print(f"\n2. Module discovery with subpackages:")
        all_modules = generator.discover_modules(include_subpackages=True)
        for module in all_modules:
            print(f"   - {module.relative_to(temp_dir)}")
        
        print(f"\n3. Generating hierarchical documentation for: {java_module.name}")
        results = generator.generate_hierarchical_documentation(java_module, force=True)
        
        print(f"\nGeneration results:")
        for path, success in results.items():
            status = "✅" if success else "❌"
            rel_path = Path(path).relative_to(temp_dir)
            print(f"   {status} {rel_path}")
        
        print(f"\n4. Generated documentation files:")
        for path in results.keys():
            doc_file = Path(path) / ".ac.mod.md"
            if doc_file.exists():
                rel_path = doc_file.relative_to(temp_dir)
                print(f"   📄 {rel_path}")
        
        print(f"\n5. Demonstrating batch hierarchical generation:")
        all_results = generator.generate_all_hierarchical_documentation(force=True)
        
        total_modules = len(all_results)
        total_documents = sum(len(results) for results in all_results.values())
        successful_documents = sum(
            sum(1 for success in results.values() if success) 
            for results in all_results.values()
        )
        
        print(f"   Main modules processed: {total_modules}")
        print(f"   Total documents generated: {successful_documents}/{total_documents}")
        
        print(f"\n6. Documentation structure overview:")
        for main_module, module_results in all_results.items():
            rel_main = Path(main_module).relative_to(temp_dir)
            print(f"   📁 {rel_main}:")
            for doc_path, success in module_results.items():
                status = "✅" if success else "❌"
                if doc_path == main_module:
                    print(f"      {status} 📄 .ac.mod.md (main module)")
                else:
                    rel_sub = Path(doc_path).relative_to(Path(main_module))
                    print(f"      {status} 📄 {rel_sub}/.ac.mod.md")
        
    finally:
        # Cleanup
        shutil.rmtree(temp_dir)
        print(f"\nCleaned up temporary directory: {temp_dir}")


if __name__ == "__main__":
    demonstrate_hierarchical_generation()

```

```python
#!/usr/bin/env python3
"""
Example demonstrating how to use AC Module Generator with Java projects
"""

import sys
sys.path.append('.')

from pathlib import Path
from generator import ACModGenerator

def generate_java_project_documentation():
    """
    Example: Generate documentation for a Java project
    """
    print("🚀 Java 项目文档生成示例")
    print("=" * 50)
    
    # 假设您有一个 Java 项目路径
    # 这里我们使用当前目录作为示例，您可以替换为实际的 Java 项目路径
    java_project_path = Path("/path/to/your/java/project")
    
    # 如果路径不存在，我们创建一个示例说明
    if not java_project_path.exists():
        print("📝 使用说明：")
        print("请将 java_project_path 替换为您的实际 Java 项目路径")
        print()
        print("支持的 Java 项目结构：")
        print("1. Maven 项目 (包含 pom.xml)")
        print("2. Gradle 项目 (包含 build.gradle 或 build.gradle.kts)")
        print("3. 标准 Java 项目 (包含 .java 文件)")
        print()
        
        # 使用当前目录作为根路径进行演示
        root_path = Path.cwd().parent.parent.parent  # 回到项目根目录
        print(f"演示：扫描 {root_path} 目录...")
        
        # 创建生成器实例
        generator = ACModGenerator(root_path)
        
        # 发现潜在的模块
        print("\n🔍 发现的潜在模块：")
        modules = generator.discover_modules()
        
        for i, module in enumerate(modules[:5], 1):  # 只显示前5个
            print(f"{i}. {module}")
            
            # 分析模块
            analysis = generator.analyze_module(module)
            
            # 显示分析结果
            print(f"   📁 目录结构: {len(analysis.get('directory_structure', {}).get('children', []))} 个子项")
            print(f"   🔧 核心组件: {len(analysis.get('core_components', []))} 个")
            print(f"   📖 使用示例: {len(analysis.get('usage_examples', []))} 个")
            print(f"   🧪 测试命令: {len(analysis.get('test_commands', []))} 个")
            
            # 检查是否包含 Java 文件
            java_components = [c for c in analysis.get('core_components', []) 
                             if c.get('file', '').endswith('.java')]
            if java_components:
                print(f"   ☕ Java 组件: {len(java_components)} 个")
                for comp in java_components[:3]:  # 显示前3个
                    print(f"      - {comp.get('type', 'unknown')}: {comp.get('name', 'unnamed')}")
            
            print()
        
        return
    
    # 实际的 Java 项目处理
    print(f"📂 分析 Java 项目: {java_project_path}")
    
    # 创建生成器实例
    generator = ACModGenerator(java_project_path.parent)
    
    # 分析指定模块
    analysis = generator.analyze_module(java_project_path)
    
    print("\n📊 分析结果：")
    print(f"模块名称: {analysis.get('module_name', 'Unknown')}")
    print(f"模块描述: {analysis.get('module_description', 'No description')}")
    
    # 显示核心组件
    components = analysis.get('core_components', [])
    if components:
        print(f"\n🔧 核心组件 ({len(components)} 个):")
        for comp in components:
            comp_type = comp.get('type', 'unknown')
            comp_name = comp.get('name', 'unnamed')
            comp_desc = comp.get('description', 'No description')
            print(f"  - {comp_type}: {comp_name}")
            print(f"    {comp_desc}")
            
            # 显示方法（如果是类）
            methods = comp.get('methods', [])
            if methods:
                print(f"    方法: {', '.join(m.get('name', '') for m in methods[:3])}")
                if len(methods) > 3:
                    print(f"    ... 还有 {len(methods) - 3} 个方法")
            print()
    
    # 显示使用示例
    examples = analysis.get('usage_examples', [])
    if examples:
        print(f"📖 使用示例 ({len(examples)} 个):")
        for example in examples:
            file_name = example.get('file', 'unknown')
            language = example.get('language', 'unknown')
            print(f"  - {file_name} ({language})")
        print()
    
    # 显示测试命令
    test_commands = analysis.get('test_commands', [])
    if test_commands:
        print(f"🧪 测试命令:")
        for cmd in test_commands:
            print(f"  - {cmd}")
        print()
    
    # 生成文档
    print("📝 生成 .ac.mod.md 文档...")
    success = generator.generate_documentation(java_project_path, force=True)
    
    if success:
        ac_mod_file = java_project_path / ".ac.mod.md"
        print(f"✅ 文档生成成功: {ac_mod_file}")
        
        # 显示生成的文档的前几行
        if ac_mod_file.exists():
            with open(ac_mod_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                print("\n📄 生成的文档预览:")
                print("-" * 40)
                for line in lines[:15]:  # 显示前15行
                    print(line.rstrip())
                if len(lines) > 15:
                    print("...")
                print("-" * 40)
    else:
        print("❌ 文档生成失败")

def show_java_support_features():
    """显示 Java 支持的功能特性"""
    print("\n☕ Java 项目支持特性：")
    print("=" * 50)
    
    features = [
        "🔍 自动检测 Maven 项目 (pom.xml)",
        "🔍 自动检测 Gradle 项目 (build.gradle, build.gradle.kts)",
        "📁 识别标准 Java 项目结构 (src/main/java, src/test/java)",
        "🔧 解析 Java 类和接口定义",
        "📖 提取 Javadoc 注释作为组件描述",
        "🧪 识别 JUnit 测试文件 (*Test.java)",
        "📝 生成 Maven/Gradle 测试命令",
        "📋 提取使用示例 (Example*.java, Demo*.java)",
        "🏗️ 支持多模块 Java 项目",
        "🔄 与 Python 项目混合支持"
    ]
    
    for feature in features:
        print(f"  {feature}")
    
    print("\n📋 支持的文件类型：")
    print("  - .java (Java 源文件)")
    print("  - pom.xml (Maven 配置)")
    print("  - build.gradle / build.gradle.kts (Gradle 配置)")
    print("  - *Test.java (JUnit 测试文件)")
    print("  - Example*.java, Demo*.java (示例文件)")

if __name__ == "__main__":
    show_java_support_features()
    print()
    generate_java_project_documentation()

```

```python
"""
Example usage of AC Module Generator with Java context limiting

This example demonstrates how to use the enhanced AC Module Generator
to handle large Java modules that might exceed context limits.
"""

from pathlib import Path
from generator import ACModGenerator
from context_limiter import ContextLimitConfig

def example_basic_usage():
    """Basic example of using context limiting."""
    print("=== Basic Context Limiting Example ===")
    
    # Path to your Java module
    module_path = Path("./src/my_large_java_module")
    
    # Create context configuration
    context_config = ContextLimitConfig(
        max_tokens=24000,  # Limit to 24K tokens
        safe_zone_tokens=6000,  # Reserve 6K tokens for generation
        java_file_priority=True,  # Prioritize Java files
        include_test_files=False,  # Exclude test files
        max_files_per_type=30  # Max 30 files per type
    )
    
    # Create generator with context limiting
    generator = ACModGenerator(
        root_path=".",
        context_config=context_config
    )
    
    # Check if module exceeds limits
    result = generator.check_module_context_limits(module_path, max_tokens=24000)
    
    print(f"Module: {module_path}")
    print(f"Exceeds limit: {result['exceeds_limit']}")
    print(f"Total files: {result['total_files']}")
    print(f"Total tokens: {result['total_tokens']:,}")
    
    if result['exceeds_limit']:
        print(f"Recommended strategy: {result['recommended_strategy']}")
        
        # Generate with context limiting
        success = generator.generate_documentation_with_context_limiting(
            module_path, 
            force=True, 
            max_tokens=24000
        )
        
        if success:
            print("✓ Documentation generated with context limiting")
        else:
            print("✗ Failed to generate documentation")
    else:
        print("✓ Module is within limits, generating normally")
        success = generator.generate_documentation(module_path)

def example_cli_usage():
    """Example of using the CLI with context limiting."""
    print("\n=== CLI Usage Examples ===")
    
    print("1. Check context limits for a module:")
    print("   python -m ac_mod_generator.cli check-context ./src/my_java_module --show-files")
    
    print("\n2. Generate documentation with context limiting:")
    print("   python -m ac_mod_generator.cli generate ./src/my_java_module --enable-context-limiting --max-tokens 20000")
    
    print("\n3. Generate documentation including test files:")
    print("   python -m ac_mod_generator.cli generate ./src/my_java_module --enable-context-limiting --include-tests")

def example_advanced_configuration():
    """Advanced configuration example."""
    print("\n=== Advanced Configuration Example ===")
    
    # Custom context configuration for different scenarios
    configs = {
        "small_model": ContextLimitConfig(
            max_tokens=8000,
            safe_zone_tokens=2000,
            java_file_priority=True,
            include_test_files=False,
            max_files_per_type=15
        ),
        "large_model": ContextLimitConfig(
            max_tokens=128000,
            safe_zone_tokens=16000,
            java_file_priority=True,
            include_test_files=True,
            max_files_per_type=100
        ),
        "focused_analysis": ContextLimitConfig(
            max_tokens=16000,
            safe_zone_tokens=4000,
            java_file_priority=True,
            include_test_files=False,
            max_files_per_type=20
        )
    }
    
    module_path = Path("./src/my_java_module")
    
    for config_name, config in configs.items():
        print(f"\n--- {config_name.replace('_', ' ').title()} Configuration ---")
        
        generator = ACModGenerator(".", context_config=config)
        result = generator.check_module_context_limits(module_path, config.max_tokens)
        
        print(f"Max tokens: {config.max_tokens:,}")
        print(f"Exceeds limit: {result['exceeds_limit']}")
        print(f"Total tokens: {result['total_tokens']:,}")
        
        if result['exceeds_limit']:
            percentage_over = ((result['total_tokens'] - config.max_tokens) / config.max_tokens) * 100
            print(f"Over limit by: {percentage_over:.1f}%")

def example_batch_processing():
    """Example of batch processing multiple Java modules."""
    print("\n=== Batch Processing Example ===")
    
    # List of Java modules to process
    java_modules = [
        "./src/core",
        "./src/api",
        "./src/services",
        "./src/utils",
        "./src/models"
    ]
    
    # Configuration for batch processing
    config = ContextLimitConfig(
        max_tokens=20000,
        safe_zone_tokens=5000,
        java_file_priority=True,
        include_test_files=False
    )
    
    generator = ACModGenerator(".", context_config=config)
    
    results = []
    
    for module_path_str in java_modules:
        module_path = Path(module_path_str)
        
        if not module_path.exists():
            print(f"⚠️  Module not found: {module_path}")
            continue
        
        print(f"\nProcessing: {module_path}")
        
        # Check context limits
        result = generator.check_module_context_limits(module_path, config.max_tokens)
        
        if result['exceeds_limit']:
            print(f"  ⚠️  Exceeds limit ({result['total_tokens']:,} tokens)")
            success = generator.generate_documentation_with_context_limiting(
                module_path, force=True, max_tokens=config.max_tokens
            )
        else:
            print(f"  ✓ Within limit ({result['total_tokens']:,} tokens)")
            success = generator.generate_documentation(module_path, force=True)
        
        results.append({
            "module": str(module_path),
            "success": success,
            "exceeds_limit": result['exceeds_limit'],
            "total_tokens": result['total_tokens']
        })
    
    # Summary
    print(f"\n=== Batch Processing Summary ===")
    successful = sum(1 for r in results if r['success'])
    total = len(results)
    
    print(f"Processed: {successful}/{total} modules")
    
    for result in results:
        status = "✓" if result['success'] else "✗"
        limit_status = "⚠️" if result['exceeds_limit'] else "✓"
        print(f"  {status} {result['module']} {limit_status} ({result['total_tokens']:,} tokens)")

if __name__ == "__main__":
    print("AC Module Generator - Java Context Limiting Examples")
    print("=" * 60)
    
    # Run examples
    example_basic_usage()
    example_cli_usage()
    example_advanced_configuration()
    example_batch_processing()
    
    print("\n" + "=" * 60)
    print("Examples completed. See the generated .ac.mod.md files in your modules.")

```

```python
        """Test template-based documentation generation (no LLM)"""
        generator = ACModGenerator(root_path=self.temp_dir)
        success = generator.generate_documentation(self.test_module_path, force=True)
        self.assertTrue(success)
        doc_path = self.test_module_path / ".ac.mod.md"
        self.assertTrue(doc_path.exists())
        content = doc_path.read_text()
        self.assertIn("test_module", content)
        self.assertIn("目录结构", content)
        """Test LLM configuration creation"""
        config = LLMConfig(
            api_base="https://api.openai.com/v1",
            api_key="test_key",
            model="gpt-3.5-turbo"
        )
        self.assertEqual(config.api_base, "https://api.openai.com/v1")
        self.assertEqual(config.api_key, "test_key")
        self.assertEqual(config.model, "gpt-3.5-turbo")
        """Test generator initialization with LLM config"""
        llm_config = LLMConfig(api_key="test_key")
```

```python
    """Test that Java projects are properly detected."""
    print("Testing Java project detection...")
    temp_dir = create_test_java_project()
    try:
        scanner = ModuleScanner(temp_dir)
        modules = scanner.find_potential_modules()
        print(f"Found {len(modules)} potential modules: {modules}")
        file_utils = FileUtils()
        is_java = file_utils.is_java_project(temp_dir)
        print(f"Is Java project: {is_java}")
        components = scanner.extract_core_components(temp_dir)
        print(f"Found {len(components)} components:")
        for comp in components:
            print(f"  - {comp['type']}: {comp['name']} ({comp.get('description', 'No description')})")
        class_names = [comp['name'] for comp in components if comp['type'] == 'class']
        examples = scanner.find_usage_examples(temp_dir)
        print(f"Found {len(examples)} usage examples:")
        for example in examples:
            print(f"  - {example['file']} ({example['language']})")
        test_commands = scanner.find_test_commands(temp_dir)
```

```python
        """Test default configuration"""
        config = LLMConfig()
        self.assertEqual(config.api_base, "https://api.openai.com/v1")
        self.assertEqual(config.model, "gpt-3.5-turbo")
        self.assertEqual(config.max_tokens, 4000)
        self.assertEqual(config.temperature, 0.1)
        """Test custom configuration"""
        config = LLMConfig(
            api_base="http://localhost:8000/v1",
            api_key="test_key",
            model="llama2",
            max_tokens=2000,
            temperature=0.5
        )
        self.assertEqual(config.api_base, "http://localhost:8000/v1")
        self.assertEqual(config.api_key, "test_key")
        self.assertEqual(config.model, "llama2")
        self.assertEqual(config.max_tokens, 2000)
        self.assertEqual(config.temperature, 0.5)
        """Test configuration from environment variables"""
```

```python
    """Test the context limiting functionality."""
    print("Testing Context Limiting Functionality")
    print("=" * 50)
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        print("Creating test Java module with 30 files...")
        module_path = create_test_java_module(temp_path, num_files=30)
        print(f"\nAnalyzing module: {module_path}")
        for max_tokens in [8000, 16000, 32000]:
            print(f"\n--- Testing with {max_tokens:,} token limit ---")
            config = ContextLimitConfig(max_tokens=max_tokens)
            limiter = ContextLimiter(config)
            analysis = limiter.analyze_module(module_path)
            summary = limiter.get_context_summary(analysis)
            print(summary)
            if analysis.exceeds_limit:
                print(f"✓ Correctly detected context limit exceeded")
                print(f"  Recommended strategy: {analysis.recommended_strategy}")
                conversations = [{"role": "user", "content": "Generate documentation for this Java module"}]
                limited_sources, _ = limiter.limit_module_content(module_path, conversations)
```

```python
        """场景1: 没有配置API密钥 - 直接使用模板"""
        print("\n=== 场景1: 没有API密钥 ===")
        generator = ACModGenerator(root_path=self.temp_dir)
        success = generator.generate_documentation(self.test_module_path, force=True)
        self.assertTrue(success)
        doc_path = self.test_module_path / ".ac.mod.md"
        content = doc_path.read_text()
        self.assertIn("test_module", content)
        self.assertIn("目录结构", content)
        print("✓ 成功使用模板生成文档")
        """场景2: API密钥无效 - LLM调用失败，回退到模板"""
        print("\n=== 场景2: API密钥无效 ===")
        llm_config = LLMConfig(
            api_base="https://api.openai.com/v1",
            api_key="invalid_key",
            model="gpt-3.5-turbo"
        )
        generator = ACModGenerator(root_path=self.temp_dir, llm_config=llm_config)
        with patch('openai.OpenAI') as mock_openai:
            mock_client = Mock()
```

```python
    """Test basic Java module documentation generation."""
    print("=== Testing Basic Functionality ===")
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        project_path = create_test_java_project(temp_path, num_files=5)
        generator = ACModGenerator(temp_path)
        success = generator.generate_documentation(project_path)
        if success:
            doc_path = project_path / ".ac.mod.md"
            if doc_path.exists():
                print("✓ Basic documentation generation successful")
                content = doc_path.read_text()
                if "Application" in content and "TestClass" in content:
                    print("✓ Documentation contains expected Java classes")
                else:
                    print("✗ Documentation missing expected content")
            else:
                print("✗ Documentation file not created")
        else:
            print("✗ Basic documentation generation failed")
```

```python
        """Test scanner initialization."""
        """Test finding modules in empty directory."""
        modules = self.scanner.find_potential_modules()
        """Test finding Python package modules."""
        package_dir = self.temp_dir / "test_package"
        package_dir.mkdir()
        (package_dir / "__init__.py").write_text("# Package init")
        (package_dir / "module.py").write_text("def function(): pass")
        modules = self.scanner.find_potential_modules()
        """Test finding modules with multiple Python files."""
        module_dir = self.temp_dir / "test_module"
        module_dir.mkdir()
        (module_dir / "file1.py").write_text("def func1(): pass")
        (module_dir / "file2.py").write_text("def func2(): pass")
        modules = self.scanner.find_potential_modules()
        """Test that hidden directories are skipped."""
        hidden_dir = self.temp_dir / ".hidden"
        hidden_dir.mkdir()
        (hidden_dir / "file.py").write_text("def func(): pass")
        modules = self.scanner.find_potential_modules()
```

```python
        """Test engine initialization with default template."""
        engine = TemplateEngine()
        """Test engine initialization with custom template."""
        template_path = str(self.temp_dir / "custom.md")
        engine = TemplateEngine(template_path)
        """Test rendering with basic analysis data."""
        analysis = {
            "name": "test_module",
            "description": "A test module for testing",
            "path": "src/test_module",
            "directory_structure": {
                "name": "test_module/",
                "children": [
                    {"name": "__init__.py", "description": "Module init"},
                    {"name": "main.py", "description": "Main module"}
                ]
            },
            "core_components": [
                {
                    "name": "TestClass",
```

```python
        """Test generator initialization."""
        """Test module discovery in empty directory."""
        modules = self.generator.discover_modules()
        """Test module discovery with Python package."""
        package_dir = self.temp_dir / "test_package"
        package_dir.mkdir()
        (package_dir / "__init__.py").write_text("# Test package")
        (package_dir / "module.py").write_text("def test_function(): pass")
        modules = self.generator.discover_modules()
        """Test module analysis."""
        module_dir = self.temp_dir / "test_module"
        module_dir.mkdir()
        init_content = '''"""Test module for analysis."""
class TestClass:
    """A test class."""
        """A test method."""
        pass
    """A test function."""
    pass
'''
```

### 辅助函数说明

[详细说明模块提供的辅助函数]

### 配置管理

[说明配置选项和管理方式]

## 核心组件详解

### 1. count_tokens

Mock token counter - estimates 4 chars per token

### 2. SourceCode

### 3. AutoCoderArgs

### 4. PruneContext

**主要方法:**
- `handle_overflow()`: 

### 5. ModuleAnalysisResult

Result of module analysis for context limiting

### 6. ContextLimitConfig

Configuration for context limiting

### 7. ContextLimiter

**主要方法:**
- `analyze_module()`: 
- `limit_module_content()`: 
- `get_context_summary()`: Get a human-readable summary of the context analysis.

### 8. ModuleScanner

**主要方法:**
- `find_potential_modules()`: 
- `get_directory_structure()`: 
- `extract_core_components()`: 
- `find_dependencies()`: 
- `find_usage_examples()`: 
- `find_test_commands()`: 
- `extract_module_description()`: 

### 9. build_tree

### 10. create_sample_java_project

Create a sample Java project structure for demonstration.

### 11. demonstrate_hierarchical_generation

Demonstrate hierarchical documentation generation.

### 12. FileUtils

**主要方法:**
- `read_file()`: 
- `write_file()`: 
- `list_files()`: 
- `list_directories()`: 
- `get_file_info()`: 
- `find_files_by_extension()`: 
- `is_python_package()`: 
- `is_git_repository()`: 
- `is_java_project()`: 
- `get_relative_path()`: 
- `backup_file()`: 
- `safe_filename()`: 
- `ensure_directory()`: 

### 13. LLMConfig

Configuration for LLM API calls

### 14. LLMClient

**主要方法:**
- `chat_completion()`: 
- `generate_documentation()`: 
- `is_available()`: Check if LLM client is available and configured

### 15. create_llm_client

### 16. setup_logging

Setup logging for the example.

### 17. example_basic_usage

Example 1: Basic usage - generate documentation for a single module.

### 18. example_discover_modules

Example 2: Discover all potential modules in the project.

### 19. example_batch_generation

Example 3: Batch generate documentation for all modules.

### 20. example_custom_template

Example 4: Using a custom template.

### 21. example_update_existing

Example 5: Update existing documentation.

### 22. example_component_analysis

Example 6: Detailed component analysis.

### 23. main

Run all examples.

### 24. generate_java_project_documentation

### 25. show_java_support_features

显示 Java 支持的功能特性

### 26. create_demo_module

创建一个演示模块

### 27. demonstrate_data_collection

演示数据收集过程

### 28. demonstrate_template_rendering

演示模板渲染过程

### 29. demonstrate_final_output

演示最终输出

### 30. main

主演示函数

### 31. example_basic_usage

Basic example of using context limiting.

### 32. example_cli_usage

Example of using the CLI with context limiting.

### 33. example_advanced_configuration

Advanced configuration example.

### 34. example_batch_processing

Example of batch processing multiple Java modules.

### 35. setup_logging

Setup logging configuration.

### 36. generate_single_module

Generate documentation for a single module.

### 37. generate_all_modules

Generate documentation for all discovered modules.

### 38. update_module

Update existing documentation for a module.

### 39. list_modules

List all discovered modules.

### 40. check_context_limits

Check context limits for a module without generating documentation.

### 41. main

Main entry point for the CLI.

### 42. ACModGenerator

**主要方法:**
- `discover_modules()`: 
- `analyze_module()`: 
- `generate_documentation()`: 
- `generate_all_documentation()`: 
- `update_existing_documentation()`: 
- `generate_hierarchical_documentation()`: 
- `generate_all_hierarchical_documentation()`: 
- `generate_documentation_with_context_limiting()`: 
- `check_module_context_limits()`: 
- `set_llm_config()`: 

### 43. TemplateEngine

**主要方法:**
- `render()`: 
- `update_existing()`: 

### 44. render_tree

## 依赖关系说明

- /home/<USER>/fengbin/DailyWork/ZeroAgents/业务语义/ac_mod_generator_v1/.ac.mod.md

## 可以验证模块可运行的测试命令

```bash
pytest ac_mod_generator -v
```
