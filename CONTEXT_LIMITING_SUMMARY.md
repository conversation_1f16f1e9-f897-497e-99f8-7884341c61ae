# Java 模块上下文限制功能总结

## 概述

为了解决大型 Java 模块在生成 `.ac.mod.md` 文档时可能超过大语言模型上下文窗口限制的问题，我们在 AC Module Generator 中新增了智能的上下文限制功能。

## 新增功能

### 1. 核心组件

#### `ContextLimiter` 类 (`context_limiter.py`)
- **功能**: 智能分析和限制模块内容，防止上下文超限
- **特性**:
  - 文件类型检测和优先级排序
  - Token 计数和分析
  - 多种裁剪策略（score/extract/delete）
  - 滑动窗口处理超大文件

#### `ContextLimitConfig` 配置类
- **功能**: 配置上下文限制的各种参数
- **主要参数**:
  - `max_tokens`: 最大 Token 限制（默认 32000）
  - `safe_zone_tokens`: 预留生成空间（默认 8000）
  - `java_file_priority`: Java 文件优先处理
  - `include_test_files`: 是否包含测试文件
  - `max_files_per_type`: 每种类型最大文件数

### 2. 增强的生成器功能

#### `ACModGenerator` 类增强
- **新方法**:
  - `generate_documentation_with_context_limiting()`: 带上下文限制的文档生成
  - `check_module_context_limits()`: 检查模块是否超过限制
  - `analyze_module()`: 增强的模块分析（支持上下文限制）

#### 智能 Java 文件处理
- **文件优先级**:
  1. 主类（Application.java, Main.java）
  2. 接口文件
  3. 服务类（Service）
  4. 控制器类（Controller）
  5. 模型类（Entity, DTO）
  6. 工具类（Utils, Helper）
  7. 其他类

### 3. 命令行工具增强

#### 新增命令
- `check-context`: 检查模块上下文限制
  ```bash
  python -m ac_mod_generator.cli check-context ./src/java/module --show-files
  ```

#### 新增参数
- `--enable-context-limiting`: 启用上下文限制
- `--max-tokens`: 设置最大 Token 限制
- `--include-tests`: 包含测试文件

## 技术实现

### 1. 上下文分析流程

```mermaid
flowchart TD
    A[开始分析模块] --> B[扫描所有文件]
    B --> C[按类型分类文件]
    C --> D[计算每个文件的Token数]
    D --> E{总Token数是否超限?}
    E -->|否| F[直接生成文档]
    E -->|是| G[应用上下文限制]
    G --> H[文件优先级排序]
    H --> I[选择裁剪策略]
    I --> J[执行内容裁剪]
    J --> K[生成限制后的文档]
```

### 2. 裁剪策略

#### Score 策略（推荐）
- 使用 LLM 对文件相关性评分
- 保留评分最高的文件
- 适用于文件数量适中的模块

#### Extract 策略
- 从大文件中提取相关代码片段
- 使用滑动窗口处理超大文件
- 适用于包含大文件的模块

#### Delete 策略
- 简单删除超出限制的文件
- 最后的备选方案

### 3. 集成现有系统

#### 与 PruneContext 集成
- 复用现有的上下文裁剪逻辑
- 支持对话驱动的代码提取
- 兼容现有的 Token 计数机制

#### 与 TokenCounter 集成
- 使用统一的 Token 计数方法
- 支持批量文件处理
- 内存优化的大文件处理

## 使用场景

### 1. 小型 Java 模块（< 20 文件）
```python
# 标准生成，无需特殊配置
generator = ACModGenerator()
generator.generate_documentation(module_path)
```

### 2. 中型 Java 模块（20-50 文件）
```python
# 启用基本上下文限制
config = ContextLimitConfig(max_tokens=24000)
generator = ACModGenerator(context_config=config)
generator.generate_documentation_with_context_limiting(module_path)
```

### 3. 大型 Java 模块（50+ 文件）
```python
# 激进的上下文限制
config = ContextLimitConfig(
    max_tokens=16000,
    max_files_per_type=20,
    include_test_files=False
)
generator = ACModGenerator(context_config=config)
generator.generate_documentation_with_context_limiting(module_path)
```

## 配置建议

### 根据模型选择配置

| 模型类型 | max_tokens | safe_zone_tokens | 适用场景 |
|----------|------------|------------------|----------|
| GPT-3.5 | 8000 | 2000 | 小型项目 |
| GPT-4 | 24000 | 6000 | 中型项目 |
| Claude-3 | 32000 | 8000 | 大型项目 |
| 本地模型 | 4000 | 1000 | 资源受限 |

### 根据项目特点配置

| 项目特点 | 推荐配置 |
|----------|----------|
| Spring Boot 项目 | `java_file_priority=True`, 排除测试文件 |
| 微服务项目 | 较低的 `max_files_per_type` |
| 工具库项目 | 包含测试文件，使用 extract 策略 |
| 遗留代码项目 | 激进的文件数量限制 |

## 测试和验证

### 1. 单元测试
- `test_context_limiting.py`: 完整的功能测试
- `test_integration.py`: 集成测试

### 2. 示例项目
- `java_context_example.py`: 使用示例
- `test_java_support.py`: Java 支持测试

### 3. 性能测试
- 支持处理 100+ 文件的大型项目
- 内存使用优化
- 处理时间在可接受范围内

## 未来改进

### 1. 短期改进
- 支持更多 Java 框架的特殊处理（Spring Boot, Quarkus）
- 改进 Javadoc 解析
- 增加注解分析

### 2. 长期规划
- 支持其他语言（C#, Go, Rust）
- 智能依赖分析
- 与 IDE 集成

## 总结

新增的上下文限制功能有效解决了大型 Java 模块文档生成时的上下文超限问题，通过智能的文件优先级、多种裁剪策略和灵活的配置选项，确保了文档生成的质量和效率。

### 主要优势

1. **智能化**: 自动识别重要文件，优先处理核心组件
2. **灵活性**: 多种配置选项适应不同项目需求
3. **兼容性**: 与现有系统无缝集成
4. **可扩展性**: 易于扩展到其他语言和框架

### 使用建议

1. 对于新项目，建议从默认配置开始
2. 根据实际效果调整 Token 限制和文件数量
3. 大型项目优先使用 extract 策略
4. 定期检查生成的文档质量，调整配置参数
