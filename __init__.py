"""
AC Module Documentation Generator

A standalone component for generating .ac.mod.md files for Auto-Coder modules.
This component provides functionality to discover modules, analyze code structure,
and generate standardized documentation.
"""

from .generator import ACModGenerator
from .template_engine import TemplateEngine
from .file_utils import FileUtils
from .module_scanner import ModuleScanner
from .llm_config import LLMConfig, LLMClient

__version__ = "1.1.0"
__all__ = ["ACModGenerator", "TemplateEngine", "FileUtils", "ModuleScanner", "LLMConfig", "LLMClient"]
