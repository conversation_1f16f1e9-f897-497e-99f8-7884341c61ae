#!/usr/bin/env python3
"""
调试LLM配置和连接
"""

import logging
from llm_config import LLMConfig, LLMClient

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_llm_config():
    """测试LLM配置"""
    print("🔧 测试LLM配置")
    print("=" * 50)
    
    # 创建配置
    config = LLMConfig(
        api_base="http://************:30804/qwen3-235b-fp8-openai-server/v1/v1",
        api_key="10298467",
        model="/Qwen2-235B-A22B-FP8"
    )
    
    print(f"📋 配置信息:")
    print(f"   API Base: {config.api_base}")
    print(f"   API Key: {config.api_key}")
    print(f"   Model: {config.model}")
    print(f"   Max Tokens: {config.max_tokens}")
    print(f"   Temperature: {config.temperature}")
    
    # 创建客户端
    print(f"\n🤖 创建LLM客户端...")
    client = LLMClient(config)
    
    # 检查可用性
    print(f"✅ 客户端可用: {client.is_available()}")
    
    # 测试简单的聊天完成
    print(f"\n💬 测试聊天完成...")
    
    messages = [
        {
            "role": "system",
            "content": "你是一个技术文档专家。"
        },
        {
            "role": "user",
            "content": "请简单介绍一下Java Spring框架，用中文回答，不超过100字。"
        }
    ]
    
    try:
        response = client.chat_completion(messages)
        if response:
            print("✅ LLM调用成功!")
            print(f"📄 响应内容:")
            print(f"   {response}")
            return True
        else:
            print("❌ LLM调用失败，返回None")
            return False
    except Exception as e:
        print(f"❌ LLM调用异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_documentation_generation():
    """测试文档生成"""
    print(f"\n📝 测试文档生成")
    print("=" * 50)
    
    config = LLMConfig(
        api_base="http://************:30804/qwen3-235b-fp8-openai-server/v1/v1",
        api_key="10298467",
        model="/Qwen2-235B-A22B-FP8"
    )
    
    client = LLMClient(config)
    
    # 模拟模块信息
    module_info = {
        "name": "test_module",
        "path": "./test_module",
        "description": "这是一个测试模块",
        "directory_structure": {
            "name": "test_module",
            "children": [
                {"name": "src", "type": "directory"},
                {"name": "README.md", "type": "file"}
            ]
        },
        "core_components": [
            {
                "name": "TestClass",
                "type": "class",
                "description": "测试类",
                "file": "src/TestClass.java"
            }
        ],
        "dependencies": ["spring-boot", "junit"],
        "usage_examples": ["TestClass testClass = new TestClass();"],
        "test_commands": ["mvn test"]
    }
    
    print("📊 模块信息:")
    print(f"   名称: {module_info['name']}")
    print(f"   路径: {module_info['path']}")
    print(f"   组件数: {len(module_info['core_components'])}")
    print(f"   依赖数: {len(module_info['dependencies'])}")
    
    print(f"\n🤖 调用LLM生成文档...")
    
    try:
        documentation = client.generate_documentation(module_info)
        if documentation:
            print("✅ 文档生成成功!")
            print(f"📄 文档长度: {len(documentation)} 字符")
            print(f"📋 文档预览:")
            lines = documentation.split('\n')
            for i, line in enumerate(lines[:15]):
                if line.strip():
                    print(f"   {i+1}: {line}")
            if len(lines) > 15:
                print("   ...")
            return True
        else:
            print("❌ 文档生成失败，返回None")
            return False
    except Exception as e:
        print(f"❌ 文档生成异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 LLM配置调试工具")
    print("=" * 60)
    
    # 测试基本配置
    config_success = test_llm_config()
    
    if config_success:
        # 测试文档生成
        doc_success = test_documentation_generation()
        
        print(f"\n{'='*60}")
        if doc_success:
            print("✅ 所有测试通过! LLM配置正常工作")
            print("\n💡 现在可以在文档生成脚本中使用LLM功能")
        else:
            print("❌ 文档生成测试失败")
    else:
        print(f"\n{'='*60}")
        print("❌ LLM配置测试失败")
        print("\n🔧 请检查:")
        print("   1. API地址是否正确")
        print("   2. API密钥是否有效")
        print("   3. 网络连接是否正常")
        print("   4. 模型名称是否正确")

if __name__ == "__main__":
    main()
