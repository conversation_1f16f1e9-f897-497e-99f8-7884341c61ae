#!/usr/bin/env python3
"""
演示父模块总结子模块文档的功能示例
"""

from pathlib import Path
from generator import ACModGenerator
from llm_config import LLMConfig

def demonstrate_hierarchical_with_summary():
    """演示分层文档生成和父模块总结功能"""
    
    print("🚀 演示分层文档生成和父模块总结功能")
    print("=" * 60)
    
    # 使用默认的测试项目路径
    project_path = Path("/home/<USER>/fengbin/DailyWork/ZeroAgents/test_repo/PATCHER")
    
    if not project_path.exists():
        print(f"❌ 测试项目不存在: {project_path}")
        print("请确保测试项目路径正确")
        return
    
    print(f"📂 项目路径: {project_path}")
    
    # 创建生成器
    generator = ACModGenerator(root_path=project_path.parent)
    
    # 配置LLM
    llm_config = LLMConfig(
        api_base="http://10.55.57.164:30804/qwen3-235b-fp8-openai-server/v1/v1",
        api_key="10298467",
        model="/Qwen2-235B-A22B-FP8"
    )
    generator.set_llm_config(llm_config)
    
    # 查找Java模块
    scanner = generator.scanner
    if scanner._is_java_package(project_path):
        print("✅ 检测到Java项目")
        
        # 查找子包
        subpackages = scanner._find_java_subpackages(project_path)
        print(f"📦 发现 {len(subpackages)} 个子包:")
        
        for i, subpackage in enumerate(subpackages, 1):
            rel_path = subpackage.relative_to(project_path)
            java_files = list(subpackage.glob("*.java"))
            print(f"   {i}. {rel_path} ({len(java_files)} Java文件)")
        
        print(f"\n🔄 开始分层文档生成...")
        
        # 执行分层文档生成
        results = generator.generate_hierarchical_documentation(
            module_path=project_path,
            force=True,
            enable_context_limiting=True
        )
        
        # 显示结果
        print(f"\n📊 生成结果:")
        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)
        print(f"   成功: {success_count}/{total_count}")
        
        # 检查主模块文档
        main_doc = project_path / ".ac.mod.md"
        if main_doc.exists():
            print(f"\n📄 主模块文档: {main_doc}")
            
            try:
                content = main_doc.read_text(encoding='utf-8')
                
                # 检查子模块概览部分
                if "## 子模块概览" in content:
                    print("   ✅ 包含子模块概览部分")
                    
                    # 提取子模块概览部分
                    lines = content.split('\n')
                    in_submodule_section = False
                    submodule_lines = []
                    
                    for line in lines:
                        if "## 子模块概览" in line:
                            in_submodule_section = True
                            continue
                        elif line.startswith("## ") and in_submodule_section:
                            break
                        elif in_submodule_section:
                            submodule_lines.append(line)
                    
                    if submodule_lines:
                        print("   📋 子模块概览内容:")
                        for line in submodule_lines[:10]:  # 显示前10行
                            if line.strip():
                                print(f"      {line}")
                        if len(submodule_lines) > 10:
                            print("      ...")
                else:
                    print("   ❌ 未找到子模块概览部分")
                    
            except Exception as e:
                print(f"   ❌ 读取文档失败: {e}")
        else:
            print(f"   ❌ 主模块文档不存在")
        
        # 列出所有生成的文档
        print(f"\n📋 所有生成的文档:")
        for doc_path, success in results.items():
            status = "✅" if success else "❌"
            path_obj = Path(doc_path)
            doc_file = path_obj / ".ac.mod.md"
            
            if path_obj == project_path:
                print(f"   {status} 主模块: .ac.mod.md")
            else:
                rel_path = path_obj.relative_to(project_path)
                print(f"   {status} 子模块: {rel_path}/.ac.mod.md")
                
                # 检查文件是否存在
                if doc_file.exists():
                    size = doc_file.stat().st_size
                    print(f"      📄 文件大小: {size} 字节")
                else:
                    print(f"      ❌ 文件不存在")
    
    else:
        print("❌ 不是Java项目")
    
    print(f"\n{'='*60}")
    print("✅ 演示完成!")
    print("\n💡 功能特点:")
    print("   1. 先生成所有子模块的独立文档")
    print("   2. 再生成包含子模块摘要的主模块文档")
    print("   3. 主模块文档包含指向子模块文档的链接")
    print("   4. 实现了真正的分层文档结构")

if __name__ == "__main__":
    demonstrate_hierarchical_with_summary()
