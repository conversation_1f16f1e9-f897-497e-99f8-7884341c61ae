#!/usr/bin/env python3
"""
简洁的 Java 项目文档生成脚本

使用方法:
  python gen_java_doc.py <java_project_path>
  python gen_java_doc.py <java_project_path> --llm --api-key your_key
"""

import sys
import os
from pathlib import Path
from generator import ACModGenerator
from llm_config import LLMConfig


def find_java_modules(root_path: Path):
    """查找所有 Java 模块"""
    modules = []

    # 检查根目录
    if list(root_path.rglob("*.java")):
        modules.append(root_path)

    # 查找子模块
    for item in root_path.iterdir():
        if item.is_dir() and not item.name.startswith('.'):
            java_files = list(item.rglob("*.java"))
            if java_files and ((item / "pom.xml").exists() or
                              (item / "build.gradle").exists() or
                              (item / "src").exists()):
                modules.append(item)

    return modules


def generate_doc(project_path_str: str, use_llm: bool = False, api_key: str = None) -> bool:
    """生成 Java 项目文档"""
    project_path = Path(project_path_str).resolve()

    # 基本验证
    if not project_path.exists():
        print(f"❌ 路径不存在: {project_path}")
        return False

    if not project_path.is_dir():
        print(f"❌ 不是目录: {project_path}")
        return False

    # 查找模块
    modules = find_java_modules(project_path)
    if not modules:
        print("❌ 没有找到 Java 模块")
        return False

    print(f"📂 项目: {project_path.name}")
    print(f"📦 模块: {len(modules)} 个")

    # 配置 LLM
    llm_config = None
    if use_llm:
        api_key = api_key or os.getenv("OPENAI_API_KEY")
        if api_key:
            llm_config = LLMConfig(api_key=api_key)
            print("🤖 LLM 模式")
        else:
            print("⚠️  无 API 密钥，使用模板模式")

    # 生成文档
    generator = ACModGenerator(root_path=project_path, llm_config=llm_config)
    success_count = 0

    for i, module in enumerate(modules, 1):
        rel_path = module.relative_to(project_path) if module != project_path else "."
        print(f"[{i}/{len(modules)}] {rel_path}", end=" ")

        try:
            success = generator.generate_documentation(
                module_path=module,
                force=True,
                enable_context_limiting=True
            )

            if success:
                print("✅")
                success_count += 1
            else:
                print("❌")
        except Exception:
            print("❌")

    print(f"完成: {success_count}/{len(modules)}")
    return success_count > 0


def main():
    # 默认使用当前目录
    project_path = "."
    use_llm = False
    api_key = None

    # 解析命令行参数
    args = sys.argv[1:]

    # 如果第一个参数不是选项，则作为项目路径
    if args and not args[0].startswith("--"):
        project_path = args[0]
        args = args[1:]

    # 检查选项
    use_llm = "--llm" in args

    if "--api-key" in args:
        try:
            key_index = args.index("--api-key") + 1
            if key_index < len(args):
                api_key = args[key_index]
        except (ValueError, IndexError):
            pass

    # 如果没有参数且用户需要帮助
    if len(sys.argv) == 1 and not os.path.exists(project_path):
        print("使用方法:")
        print("  python gen_java_doc.py                              # 当前目录")
        print("  python gen_java_doc.py <java_project_root_path>     # 指定目录")
        print("  python gen_java_doc.py --llm                        # 当前目录 + LLM")
        print("  python gen_java_doc.py /path/to/project --llm       # 指定目录 + LLM")
        print("\n功能: 为 Java 项目的所有模块生成 .ac.mod.md 文档")

    success = generate_doc(project_path, use_llm, api_key)
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
