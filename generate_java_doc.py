#!/usr/bin/env python3
"""
Java 项目文档生成脚本

使用方法:
    python generate_java_doc.py <java_project_path> [--llm]

示例:
    # 使用模板生成
    python generate_java_doc.py /path/to/your/java/project

    # 使用 LLM 生成
    python generate_java_doc.py /path/to/your/java/project --llm

    # 使用自定义 LLM 配置
    python generate_java_doc.py /path/to/your/java/project --llm --api-base https://api.openai.com/v1 --api-key your_key --model gpt-4
"""

import sys
import os
import argparse
import logging
from pathlib import Path
from generator import ACModGenerator
from llm_config import LLMConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)


def find_java_modules(root_path: Path, max_files_per_module: int = 20):
    """
    递归查找 Java 项目中的所有模块，基于文件数量进行分层

    Args:
        root_path: 项目根目录
        max_files_per_module: 每个模块的最大文件数量阈值

    Returns:
        list: 模块路径列表
    """
    def _find_modules_recursive(current_path: Path, collected_modules: list):
        """递归查找模块"""
        # 跳过隐藏目录和常见的非源码目录
        skip_dirs = {'.git', '.idea', '.vscode', 'target', 'build', 'out', '.gradle', 'node_modules'}
        if current_path.name.startswith('.') or current_path.name in skip_dirs:
            return

        # 统计当前目录下的Java文件数量（包括子目录）
        java_files = list(current_path.rglob("*.java"))
        java_count = len(java_files)

        # 如果没有Java文件，跳过
        if java_count == 0:
            return

        # 如果Java文件数量小于等于阈值，作为一个模块
        if java_count <= max_files_per_module:
            collected_modules.append(current_path)
            return

        # 如果Java文件数量超过阈值，检查是否可以进一步分解
        subdirs_with_java = []
        for item in current_path.iterdir():
            if item.is_dir() and not item.name.startswith('.') and item.name not in skip_dirs:
                subdir_java_files = list(item.rglob("*.java"))
                if subdir_java_files:
                    subdirs_with_java.append((item, len(subdir_java_files)))

        # 如果有包含Java文件的子目录，递归处理它们
        if subdirs_with_java:
            # 检查当前目录直接包含的Java文件
            direct_java_files = list(current_path.glob("*.java"))

            # 如果当前目录直接包含Java文件，将当前目录也作为一个模块
            if direct_java_files:
                collected_modules.append(current_path)

            # 递归处理子目录
            for subdir, _ in subdirs_with_java:
                _find_modules_recursive(subdir, collected_modules)
        else:
            # 如果没有子目录包含Java文件，当前目录作为一个模块
            collected_modules.append(current_path)

    modules = []
    _find_modules_recursive(root_path, modules)

    # 去重并排序
    unique_modules = []
    for module in modules:
        if module not in unique_modules:
            unique_modules.append(module)

    # 按路径深度排序，浅层目录在前
    unique_modules.sort(key=lambda p: (len(p.parts), str(p)))

    return unique_modules


def generate_single_module_doc(module_path: Path, root_path: Path, llm_config=None, force_hierarchical=False):
    """
    为单个模块生成文档，自动判断是否需要分层生成

    Args:
        module_path: 模块路径
        root_path: 项目根路径
        llm_config: LLM 配置（可选）
        force_hierarchical: 是否强制使用分层生成

    Returns:
        bool: 生成是否成功
    """
    try:
        # 创建文档生成器
        generator = ACModGenerator(root_path=root_path, llm_config=llm_config)

        # 检查Java文件数量，决定是否使用分层生成
        java_files = list(module_path.rglob("*.java"))
        java_count = len(java_files)

        # 决定是否使用分层文档生成
        use_hierarchical = force_hierarchical or java_count > 20

        if use_hierarchical:
            print(f"   📊 检测到大型模块 ({java_count} Java 文件)，启用分层文档生成")

            # 使用分层文档生成
            results = generator.generate_hierarchical_documentation(
                module_path=module_path,
                force=True,  # 强制覆盖现有文档
                enable_context_limiting=True  # 启用上下文限制
            )

            # 统计生成结果
            success_count = sum(1 for success in results.values() if success)
            total_count = len(results)

            print(f"   📝 分层文档生成完成: {success_count}/{total_count} 个文档")

            # 显示生成的文档列表
            for doc_path, success in results.items():
                rel_path = Path(doc_path).relative_to(module_path)
                status = "✅" if success else "❌"
                if str(rel_path) == ".":
                    print(f"      {status} .ac.mod.md (主模块)")
                else:
                    print(f"      {status} {rel_path}/.ac.mod.md")

            return success_count > 0
        else:
            print(f"   📊 普通模块 ({java_count} Java 文件)，使用标准文档生成")

            # 使用标准的带上下文限制的文档生成方法
            success = generator.generate_documentation_with_context_limiting(
                module_path=module_path,
                force=True,  # 强制覆盖现有文档
                max_tokens=64000  # 设置上下文限制
            )

            return success

    except Exception as e:
        print(f"   ❌ 错误: {e}")
        return False


def generate_java_documentation_hierarchical(project_path_str: str, llm_config=None):
    """
    为指定的 Java 项目生成分层 .ac.mod.md 文档（强制分层模式）

    Args:
        project_path_str: Java 项目路径字符串
        llm_config: LLM 配置（可选）

    Returns:
        bool: 生成是否成功
    """
    # 转换为 Path 对象并解析绝对路径
    project_path = Path(project_path_str).resolve()

    print(f"🚀 开始为 Java 项目生成分层文档（强制分层模式）")
    print(f"📂 项目路径: {project_path}")

    # 验证路径
    if not project_path.exists():
        print(f"❌ 错误: 路径不存在 - {project_path}")
        return False

    if not project_path.is_dir():
        print(f"❌ 错误: 不是目录 - {project_path}")
        return False

    # 查找所有 Java 模块
    print("🔍 扫描 Java 模块...")
    modules = find_java_modules(project_path)

    if not modules:
        print("❌ 错误: 没有找到任何 Java 模块")
        return False

    print(f"✅ 找到 {len(modules)} 个 Java 模块 (基于文件数量自动分层):")
    total_files = 0
    for i, module in enumerate(modules, 1):
        rel_path = module.relative_to(project_path) if module != project_path else Path(".")
        java_count = len(list(module.rglob("*.java")))
        total_files += java_count
        depth = len(module.parts) - len(project_path.parts)
        indent = "  " * depth
        print(f"   {i}. {indent}{rel_path} ({java_count} Java 文件)")

    print(f"📊 总计: {total_files} 个 Java 文件分布在 {len(modules)} 个模块中")

    # 检查项目类型
    if (project_path / "pom.xml").exists():
        print("📦 检测到 Maven 多模块项目")
    elif (project_path / "build.gradle").exists() or (project_path / "build.gradle.kts").exists():
        print("📦 检测到 Gradle 多模块项目")
    else:
        print("📦 检测到 Java 项目")

    # 为每个模块生成分层文档
    print(f"\n📝 开始为 {len(modules)} 个模块生成分层文档...")
    success_count = 0
    failed_modules = []
    total_docs_generated = 0

    for i, module in enumerate(modules, 1):
        rel_path = module.relative_to(project_path) if module != project_path else Path(".")
        java_count = len(list(module.rglob("*.java")))
        print(f"\n[{i}/{len(modules)}] 处理模块: {rel_path} ({java_count} Java 文件)")

        try:
            # 强制使用分层生成
            success = generate_single_module_doc(module, project_path, llm_config, force_hierarchical=True)
            if success:
                success_count += 1

                # 统计实际生成的文档数量
                doc_files = list(module.rglob(".ac.mod.md"))
                docs_in_module = len(doc_files)
                total_docs_generated += docs_in_module

                print(f"   ✅ 分层文档生成成功，共 {docs_in_module} 个文档文件")
            else:
                print(f"   ❌ 生成失败")
                failed_modules.append(str(rel_path))
        except Exception as e:
            print(f"   ❌ 错误: {e}")
            failed_modules.append(str(rel_path))

    # 输出总结
    print(f"\n{'='*60}")
    print(f"📊 分层文档生成完成!")
    print(f"✅ 成功: {success_count}/{len(modules)} 个模块")
    print(f"📄 总计生成: {total_docs_generated} 个文档文件")
    print(f"❌ 失败: {len(failed_modules)} 个模块")

    if failed_modules:
        print(f"\n失败的模块:")
        for module in failed_modules:
            print(f"   - {module}")

    print(f"\n💡 提示:")
    print(f"   - 强制分层模式：所有模块都使用分层文档生成")
    print(f"   - 主模块文档: .ac.mod.md")
    print(f"   - 子包文档: <子包路径>/.ac.mod.md")
    print(f"   - 每个包含Java文件的子目录都会生成独立文档")

    return success_count > 0


def generate_java_documentation(project_path_str: str, llm_config=None):
    """
    为指定的 Java 项目生成 .ac.mod.md 文档

    Args:
        project_path_str: Java 项目路径字符串
        llm_config: LLM 配置（可选）

    Returns:
        bool: 生成是否成功
    """
    # 转换为 Path 对象并解析绝对路径
    project_path = Path(project_path_str).resolve()

    print(f"🚀 开始为 Java 项目生成文档")
    print(f"📂 项目路径: {project_path}")

    # 验证路径
    if not project_path.exists():
        print(f"❌ 错误: 路径不存在 - {project_path}")
        return False

    if not project_path.is_dir():
        print(f"❌ 错误: 不是目录 - {project_path}")
        return False

    # 查找所有 Java 模块
    print("🔍 扫描 Java 模块...")
    modules = find_java_modules(project_path)

    if not modules:
        print("❌ 错误: 没有找到任何 Java 模块")
        return False

    print(f"✅ 找到 {len(modules)} 个 Java 模块 (基于文件数量自动分层):")
    total_files = 0
    for i, module in enumerate(modules, 1):
        rel_path = module.relative_to(project_path) if module != project_path else Path(".")
        java_count = len(list(module.rglob("*.java")))
        total_files += java_count
        depth = len(module.parts) - len(project_path.parts)
        indent = "  " * depth
        print(f"   {i}. {indent}{rel_path} ({java_count} Java 文件)")

    print(f"📊 总计: {total_files} 个 Java 文件分布在 {len(modules)} 个模块中")

    # 检查项目类型
    if (project_path / "pom.xml").exists():
        print("📦 检测到 Maven 多模块项目")
    elif (project_path / "build.gradle").exists() or (project_path / "build.gradle.kts").exists():
        print("📦 检测到 Gradle 多模块项目")
    else:
        print("📦 检测到 Java 项目")

    # 为每个模块生成文档
    print(f"\n📝 开始为 {len(modules)} 个模块生成文档...")
    success_count = 0
    failed_modules = []
    total_docs_generated = 0

    for i, module in enumerate(modules, 1):
        rel_path = module.relative_to(project_path) if module != project_path else Path(".")
        java_count = len(list(module.rglob("*.java")))
        print(f"\n[{i}/{len(modules)}] 处理模块: {rel_path} ({java_count} Java 文件)")

        try:
            success = generate_single_module_doc(module, project_path, llm_config)
            if success:
                success_count += 1

                # 统计实际生成的文档数量
                doc_files = list(module.rglob(".ac.mod.md"))
                docs_in_module = len(doc_files)
                total_docs_generated += docs_in_module

                if docs_in_module > 1:
                    print(f"   ✅ 分层文档生成成功，共 {docs_in_module} 个文档文件")
                else:
                    print(f"   ✅ 标准文档生成成功")
            else:
                print(f"   ❌ 生成失败")
                failed_modules.append(str(rel_path))
        except Exception as e:
            print(f"   ❌ 错误: {e}")
            failed_modules.append(str(rel_path))

    # 输出总结
    print(f"\n� 生成完成:")
    print(f"   - 成功: {success_count}/{len(modules)} 个模块")
    print(f"   - 失败: {len(failed_modules)} 个模块")

    if failed_modules:
        print(f"   - 失败的模块: {', '.join(failed_modules)}")

    print(f"   - 总计生成: {total_docs_generated} 个文档文件")

    print(f"\n💡 提示:")
    print(f"   - 大型模块 (>20 Java文件) 自动启用分层文档生成")
    print(f"   - 主模块文档: .ac.mod.md")
    print(f"   - 子包文档: <子包路径>/.ac.mod.md")
    print(f"   - 每个包含Java文件的子目录都会生成独立文档")

    return success_count > 0


def create_llm_config(args):
    """根据命令行参数创建 LLM 配置"""

    # 优先级: 命令行参数 > 环境变量 > 默认值
    api_key = args.api_key or os.getenv("OPENAI_API_KEY")

    # 对于 api_base 和 model，如果用户没有明确指定，则检查环境变量
    api_base = args.api_base
    if api_base == "https://api.openai.com/v1":  # 如果是默认值，检查环境变量
        api_base = os.getenv("OPENAI_API_BASE", api_base)

    model = args.model
    if model == "gpt-3.5-turbo":  # 如果是默认值，检查环境变量
        model = os.getenv("OPENAI_MODEL", model)

    if not api_key:
        print("⚠️  警告: 未提供 API 密钥，将使用模板生成")
        print("   可以通过 --api-key 参数或 OPENAI_API_KEY 环境变量设置")
        return None

    print(f"🤖 使用 LLM 生成模式:")
    print(f"   - API 地址: {api_base}")
    print(f"   - 模型: {model}")
    print(f"   - API 密钥: {api_key[:8]}...{api_key[-4:] if len(api_key) > 12 else '***'}")

    return LLMConfig(
        api_base=api_base,
        api_key=api_key,
        model=model
    )


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Java 项目文档生成脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  # 使用模板生成当前目录
  python generate_java_doc.py

  # 使用模板生成指定项目
  python generate_java_doc.py /path/to/java/project

  # 使用 LLM 生成当前目录
  python generate_java_doc.py --llm

  # 使用 LLM 生成指定项目
  python generate_java_doc.py /path/to/java/project --llm

  # 使用自定义 LLM 配置
  python generate_java_doc.py /path/to/java/project --llm \\
    --api-base https://api.openai.com/v1 \\
    --api-key your_api_key \\
    --model gpt-4

环境变量:
  OPENAI_API_KEY    - OpenAI API 密钥
  OPENAI_API_BASE   - OpenAI API 地址 (默认: https://api.openai.com/v1)
  OPENAI_MODEL      - 模型名称 (默认: gpt-3.5-turbo)
        """
    )

    parser.add_argument(
        "project_path",
        nargs="?",
        default="/home/<USER>/fengbin/DailyWork/ZeroAgents/test_repo/PATCHER/daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain",
        help="Java 项目根目录路径 (默认: 当前目录)"
    )

    parser.add_argument(
        "--api-base",
        default="http://nebulacoder.dev.zte.com.cn:40081/v1/",
        help="LLM API 地址 (默认: https://api.openai.com/v1)"
    )

    parser.add_argument(
        "--api-key",
        default="10298467",
        help="LLM API 密钥 (也可通过 OPENAI_API_KEY 环境变量设置)"
    )

    parser.add_argument(
        "--model",
        default="nebulacoder-v6.0",
        help="LLM 模型名称 (默认: gpt-3.5-turbo)"
    )

    parser.add_argument(
        "--force-hierarchical",
        action="store_true",
        help="强制为所有模块使用分层文档生成，无论文件数量多少"
    )

    args = parser.parse_args()

    # 创建 LLM 配置
    llm_config = create_llm_config(args)

    # 生成文档
    if args.force_hierarchical:
        success = generate_java_documentation_hierarchical(args.project_path, llm_config)
    else:
        success = generate_java_documentation(args.project_path, llm_config)

    # 输出结果
    print("\n" + "=" * 50)
    if success:
        print("🎉 任务完成!")
    else:
        print("💥 任务失败!")
        sys.exit(1)


if __name__ == "__main__":
    main()
