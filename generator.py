"""
AC Module Documentation Generator Core Class

This module provides the main ACModGenerator class that orchestrates
the entire process of generating .ac.mod.md files.
"""

import os
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path

try:
    from .module_scanner import ModuleScanner
    from .template_engine import TemplateEngine
    from .file_utils import FileUtils
    from .context_limiter import ContextLimiter, ContextLimitConfig
    from .llm_config import LLMClient, LLMConfig
except ImportError:
    from module_scanner import ModuleScanner
    from template_engine import TemplateEngine
    from file_utils import FileUtils
    from context_limiter import ContextLimiter, ContextLimitConfig
    from llm_config import LLMClient, LLMConfig

logger = logging.getLogger(__name__)


class ACModGenerator:
    """
    Main class for generating AC module documentation.
    
    This class coordinates the entire process of:
    1. Discovering modules in a project
    2. Analyzing module structure and dependencies
    3. Generating standardized .ac.mod.md documentation
    """
    
    def __init__(self, root_path: str = ".", template_path: Optional[str] = None,
                 context_config: Optional[ContextLimitConfig] = None,
                 llm_config: Optional[LLMConfig] = None):
        """
        Initialize the AC Module Generator.

        Args:
            root_path: Root directory to scan for modules
            template_path: Optional custom template file path
            context_config: Optional context limiting configuration
            llm_config: Optional LLM configuration for AI-powered generation
        """
        self.root_path = Path(root_path).resolve()
        self.scanner = ModuleScanner(self.root_path)
        self.template_engine = TemplateEngine(template_path)
        self.file_utils = FileUtils()
        self.context_limiter = ContextLimiter(context_config)
        self.llm_client = LLMClient(llm_config)
        
    def discover_modules(self, include_subpackages: bool = False) -> List[Path]:
        """
        Discover all potential AC modules in the project.

        Args:
            include_subpackages: Whether to include Java subpackages as separate modules

        Returns:
            List of directory paths that could be AC modules
        """
        return self.scanner.find_potential_modules(include_subpackages)
    
    def analyze_module(self, module_path: Path, enable_context_limiting: bool = True) -> Dict[str, Any]:
        """
        Analyze a single module and extract its structure and metadata.

        Args:
            module_path: Path to the module directory
            enable_context_limiting: Whether to apply context limiting for large modules

        Returns:
            Dictionary containing module analysis results
        """

        # First check if context limiting is needed
        context_analysis = None
        limited_source_codes = None

        if enable_context_limiting:
            try:
                context_analysis = self.context_limiter.analyze_module(module_path)

                if context_analysis.exceeds_limit:
                    logger.warning(f"Module {module_path} exceeds context limits")
                    logger.info(self.context_limiter.get_context_summary(context_analysis))

                    # Apply context limiting
                    conversations = [{"role": "user", "content": f"Generate AC module documentation for {module_path}"}]
                    limited_source_codes, _ = self.context_limiter.limit_module_content(module_path, conversations)

                    logger.info(f"Context limiting applied: {len(limited_source_codes)} files selected")

            except Exception as e:
                logger.warning(f"Context limiting failed, proceeding without it: {e}")

        # 查找子模块文档（如果存在）
        submodule_docs = self.scanner.find_submodule_docs(module_path)
        submodule_summaries = self.scanner.extract_submodule_summaries(submodule_docs)

        analysis = {
            "name": module_path.name,
            "path": str(module_path.relative_to(self.root_path)),
            "description": self.scanner.extract_module_description_from_java_files(module_path),
            "directory_structure": self.scanner.list_files(module_path),
            "core_components": self.scanner.extract_core_components(module_path),
            "dependencies": self.scanner.find_dependencies(module_path),
            "usage_examples": self.scanner.find_usage_examples(module_path),
            "test_commands": self.scanner.find_test_commands(module_path),
            "context_analysis": context_analysis,
            "limited_source_codes": limited_source_codes,
            "submodule_docs": submodule_docs,
            "submodule_summaries": submodule_summaries
        }

        # Description is already set from Java files analysis above
        # No need to override it here

        return analysis
    
    def generate_documentation(self, module_path: Path, force: bool = False) -> bool:
        """
        Generate .ac.mod.md documentation for a single module.
        
        Args:
            module_path: Path to the module directory
            force: Whether to overwrite existing documentation
            
        Returns:
            True if documentation was generated successfully
        """
        try:
            # Check if documentation already exists
            doc_path = module_path / ".ac.mod.md"
            if doc_path.exists() and not force:
                logger.info(f"Documentation already exists: {doc_path}")
                return False
            
            # Analyze the module
            analysis = self.analyze_module(module_path)

            # Generate documentation content
            content = self._generate_content(analysis)
            
            # Write the documentation file
            self.file_utils.write_file(doc_path, content)
            
            logger.info(f"Generated documentation: {doc_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to generate documentation for {module_path}: {e}")
            return False
    
    def generate_all_documentation(self, force: bool = False) -> Dict[str, bool]:
        """
        Generate documentation for all discovered modules.
        
        Args:
            force: Whether to overwrite existing documentation
            
        Returns:
            Dictionary mapping module paths to generation success status
        """
        modules = self.discover_modules()
        results = {}
        
        logger.info(f"Found {len(modules)} potential modules")
        
        for module_path in modules:
            success = self.generate_documentation(module_path, force)
            results[str(module_path)] = success
            
        return results
    
    def update_existing_documentation(self, module_path: Path) -> bool:
        """
        Update existing .ac.mod.md documentation with new analysis.
        
        Args:
            module_path: Path to the module directory
            
        Returns:
            True if documentation was updated successfully
        """
        doc_path = module_path / ".ac.mod.md"
        if not doc_path.exists():
            logger.warning(f"No existing documentation found: {doc_path}")
            return self.generate_documentation(module_path)
        
        try:
            # Read existing content
            existing_content = self.file_utils.read_file(doc_path)
            
            # Analyze current module state
            analysis = self.analyze_module(module_path)
            
            # Update content while preserving manual modifications
            updated_content = self.template_engine.update_existing(existing_content, analysis)
            
            # Write updated content
            self.file_utils.write_file(doc_path, updated_content)
            
            logger.info(f"Updated documentation: {doc_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update documentation for {module_path}: {e}")
            return False

    def generate_hierarchical_documentation(self, module_path: Path, force: bool = False,
                                          enable_context_limiting: bool = True) -> Dict[str, bool]:
        """
        Generate documentation for a module and its Java subpackages hierarchically.

        This method is particularly useful for large Java modules where generating
        separate documentation for each functional package provides better granularity
        and maintainability.

        Args:
            module_path: Path to the main module directory
            force: Whether to overwrite existing documentation
            enable_context_limiting: Whether to apply context limiting for large modules

        Returns:
            Dictionary mapping module/subpackage paths to generation success status
        """
        results = {}

        logger.info(f"Starting hierarchical documentation generation for: {module_path}")

        # Check if this is a Java project and find subpackages
        if self.scanner._is_java_package(module_path):
            subpackages = self.scanner._find_java_subpackages(module_path)

            if subpackages:
                logger.info(f"Found {len(subpackages)} subpackages to document")

                # 先生成所有子包的文档
                for subpackage in subpackages:
                    try:
                        # Generate documentation for each subpackage
                        if enable_context_limiting:
                            sub_success = self.generate_documentation_with_context_limiting(subpackage, force)
                        else:
                            sub_success = self.generate_documentation(subpackage, force)
                        results[str(subpackage)] = sub_success

                        if sub_success:
                            logger.info(f"Generated documentation for subpackage: {subpackage}")
                        else:
                            logger.warning(f"Failed to generate documentation for subpackage: {subpackage}")

                    except Exception as e:
                        logger.error(f"Error generating documentation for subpackage {subpackage}: {e}")
                        results[str(subpackage)] = False
            else:
                logger.info("No meaningful subpackages found for separate documentation")
        else:
            logger.info("Module is not a Java project, skipping subpackage discovery")

        # 最后生成主模块文档（这样可以包含子模块的摘要）
        if enable_context_limiting:
            main_success = self.generate_documentation_with_context_limiting(module_path, force)
        else:
            main_success = self.generate_documentation(module_path, force)
        results[str(module_path)] = main_success

        # Summary
        total_modules = len(results)
        successful_modules = sum(1 for success in results.values() if success)
        logger.info(f"Hierarchical documentation generation completed: {successful_modules}/{total_modules} successful")

        return results

    def generate_all_hierarchical_documentation(self, force: bool = False,
                                              enable_context_limiting: bool = True) -> Dict[str, Dict[str, bool]]:
        """
        Generate hierarchical documentation for all discovered modules.

        This method discovers all potential modules and generates hierarchical
        documentation for each one, including their subpackages.

        Args:
            force: Whether to overwrite existing documentation
            enable_context_limiting: Whether to apply context limiting for large modules

        Returns:
            Dictionary mapping main module paths to their hierarchical generation results
        """
        # Discover main modules (without subpackages to avoid duplication)
        main_modules = self.scanner.find_potential_modules(include_subpackages=False)
        all_results = {}

        logger.info(f"Found {len(main_modules)} main modules for hierarchical documentation")

        for module_path in main_modules:
            try:
                module_results = self.generate_hierarchical_documentation(
                    module_path, force, enable_context_limiting
                )
                all_results[str(module_path)] = module_results

            except Exception as e:
                logger.error(f"Error in hierarchical documentation for {module_path}: {e}")
                all_results[str(module_path)] = {str(module_path): False}

        # Summary statistics
        total_main_modules = len(all_results)
        total_documents = sum(len(results) for results in all_results.values())
        successful_documents = sum(
            sum(1 for success in results.values() if success)
            for results in all_results.values()
        )

        logger.info(f"Hierarchical documentation generation summary:")
        logger.info(f"  Main modules processed: {total_main_modules}")
        logger.info(f"  Total documents generated: {successful_documents}/{total_documents}")

        return all_results

    def generate_documentation_with_context_limiting(self, module_path: Path, force: bool = False,
                                                   max_tokens: int = 32000) -> bool:
        """
        Generate .ac.mod.md documentation for a module with context limiting for large Java modules.

        Args:
            module_path: Path to the module directory
            force: Whether to overwrite existing documentation
            max_tokens: Maximum token limit for context

        Returns:
            True if documentation was generated successfully
        """
        try:
            # Check if documentation already exists
            doc_path = module_path / ".ac.mod.md"
            if doc_path.exists() and not force:
                logger.info(f"Documentation already exists: {doc_path}")
                return False

            # Configure context limiter for this generation
            context_config = ContextLimitConfig(max_tokens=max_tokens)
            self.context_limiter = ContextLimiter(context_config)

            # Analyze the module with context limiting enabled
            analysis = self.analyze_module(module_path, enable_context_limiting=True)

            # Log context analysis results
            if analysis.get("context_analysis"):
                context_summary = self.context_limiter.get_context_summary(analysis["context_analysis"])
                logger.info(f"Context Analysis:\n{context_summary}")

            # Generate documentation content using LLM if available, otherwise template
            content = self._generate_content(analysis)

            # Add context limiting information to the documentation if applicable
            if analysis.get("context_analysis") and analysis["context_analysis"].exceeds_limit:
                context_note = self._generate_context_limiting_note(analysis["context_analysis"])
                content = content + "\n\n" + context_note

            # Write the documentation file
            self.file_utils.write_file(doc_path, content)

            logger.info(f"Generated documentation with context limiting: {doc_path}")
            return True

        except Exception as e:
            logger.error(f"Failed to generate documentation for {module_path}: {e}")
            return False

    def _generate_context_limiting_note(self, context_analysis) -> str:
        """Generate a note about context limiting applied to the documentation."""
        note = [
            "## Context Limiting Information",
            "",
            "⚠️ **Note**: This module exceeded the context window limit during documentation generation.",
            "",
            f"- **Total files found**: {context_analysis.total_files}",
            f"- **Total tokens**: {context_analysis.total_tokens:,}",
            f"- **Strategy applied**: {context_analysis.recommended_strategy}",
            "",
            "The documentation above was generated using intelligent content selection to focus on the most important files and code sections.",
            "",
            "**File breakdown**:",
            f"- Java files: {context_analysis.java_files} ({context_analysis.java_tokens:,} tokens)",
            f"- Python files: {context_analysis.python_files} ({context_analysis.python_tokens:,} tokens)",
            f"- Other files: {context_analysis.other_files} ({context_analysis.other_tokens:,} tokens)",
        ]

        return "\n".join(note)

    def check_module_context_limits(self, module_path: Path, max_tokens: int = 32000) -> Dict[str, Any]:
        """
        Check if a module exceeds context limits without generating documentation.

        Args:
            module_path: Path to the module directory
            max_tokens: Maximum token limit to check against

        Returns:
            Dictionary with context analysis results
        """
        context_config = ContextLimitConfig(max_tokens=max_tokens)
        context_limiter = ContextLimiter(context_config)

        analysis = context_limiter.analyze_module(module_path)

        return {
            "module_path": str(module_path),
            "exceeds_limit": analysis.exceeds_limit,
            "total_files": analysis.total_files,
            "total_tokens": analysis.total_tokens,
            "recommended_strategy": analysis.recommended_strategy,
            "summary": context_limiter.get_context_summary(analysis),
            "file_breakdown": analysis.file_breakdown
        }

    def _generate_content(self, analysis: Dict[str, Any]) -> str:
        """
        Generate documentation content using LLM if available, otherwise use template.

        Args:
            analysis: Module analysis results

        Returns:
            Generated documentation content
        """
        # Try LLM generation first if available
        if self.llm_client.is_available():
            logger.info("Generating documentation using LLM")
            llm_content = self.llm_client.generate_documentation(analysis)

            if llm_content:
                logger.info("Successfully generated documentation using LLM")
                return llm_content
            else:
                logger.warning("LLM generation failed, falling back to template")

        # Fallback to template-based generation
        logger.info("Generating documentation using template")
        return self.template_engine.render(analysis)

    def set_llm_config(self, llm_config_or_api_base, api_key=None, model="gpt-3.5-turbo"):
        """
        Set LLM configuration for AI-powered documentation generation.

        Args:
            llm_config_or_api_base: Either LLMConfig object or API endpoint URL string
            api_key: API key for authentication (when first arg is string)
            model: Model name to use (when first arg is string)
        """
        if isinstance(llm_config_or_api_base, LLMConfig):
            # If LLMConfig object is passed
            self.llm_client = LLMClient(llm_config_or_api_base)
            logger.info(f"LLM configuration updated: {llm_config_or_api_base.api_base} with model {llm_config_or_api_base.model}")
        else:
            # If individual parameters are passed
            config = LLMConfig(
                api_base=llm_config_or_api_base,
                api_key=api_key,
                model=model
            )
            self.llm_client = LLMClient(config)
            logger.info(f"LLM configuration updated: {llm_config_or_api_base} with model {model}")
