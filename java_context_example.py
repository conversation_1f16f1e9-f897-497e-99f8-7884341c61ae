"""
Example usage of AC Module Generator with Java context limiting

This example demonstrates how to use the enhanced AC Module Generator
to handle large Java modules that might exceed context limits.
"""

from pathlib import Path
from generator import ACModGenerator
from context_limiter import ContextLimitConfig

def example_basic_usage():
    """Basic example of using context limiting."""
    print("=== Basic Context Limiting Example ===")
    
    # Path to your Java module
    module_path = Path("./src/my_large_java_module")
    
    # Create context configuration
    context_config = ContextLimitConfig(
        max_tokens=24000,  # Limit to 24K tokens
        safe_zone_tokens=6000,  # Reserve 6K tokens for generation
        java_file_priority=True,  # Prioritize Java files
        include_test_files=False,  # Exclude test files
        max_files_per_type=30  # Max 30 files per type
    )
    
    # Create generator with context limiting
    generator = ACModGenerator(
        root_path=".",
        context_config=context_config
    )
    
    # Check if module exceeds limits
    result = generator.check_module_context_limits(module_path, max_tokens=24000)
    
    print(f"Module: {module_path}")
    print(f"Exceeds limit: {result['exceeds_limit']}")
    print(f"Total files: {result['total_files']}")
    print(f"Total tokens: {result['total_tokens']:,}")
    
    if result['exceeds_limit']:
        print(f"Recommended strategy: {result['recommended_strategy']}")
        
        # Generate with context limiting
        success = generator.generate_documentation_with_context_limiting(
            module_path, 
            force=True, 
            max_tokens=24000
        )
        
        if success:
            print("✓ Documentation generated with context limiting")
        else:
            print("✗ Failed to generate documentation")
    else:
        print("✓ Module is within limits, generating normally")
        success = generator.generate_documentation(module_path)

def example_cli_usage():
    """Example of using the CLI with context limiting."""
    print("\n=== CLI Usage Examples ===")
    
    print("1. Check context limits for a module:")
    print("   python -m ac_mod_generator.cli check-context ./src/my_java_module --show-files")
    
    print("\n2. Generate documentation with context limiting:")
    print("   python -m ac_mod_generator.cli generate ./src/my_java_module --enable-context-limiting --max-tokens 20000")
    
    print("\n3. Generate documentation including test files:")
    print("   python -m ac_mod_generator.cli generate ./src/my_java_module --enable-context-limiting --include-tests")

def example_advanced_configuration():
    """Advanced configuration example."""
    print("\n=== Advanced Configuration Example ===")
    
    # Custom context configuration for different scenarios
    configs = {
        "small_model": ContextLimitConfig(
            max_tokens=8000,
            safe_zone_tokens=2000,
            java_file_priority=True,
            include_test_files=False,
            max_files_per_type=15
        ),
        "large_model": ContextLimitConfig(
            max_tokens=128000,
            safe_zone_tokens=16000,
            java_file_priority=True,
            include_test_files=True,
            max_files_per_type=100
        ),
        "focused_analysis": ContextLimitConfig(
            max_tokens=16000,
            safe_zone_tokens=4000,
            java_file_priority=True,
            include_test_files=False,
            max_files_per_type=20
        )
    }
    
    module_path = Path("./src/my_java_module")
    
    for config_name, config in configs.items():
        print(f"\n--- {config_name.replace('_', ' ').title()} Configuration ---")
        
        generator = ACModGenerator(".", context_config=config)
        result = generator.check_module_context_limits(module_path, config.max_tokens)
        
        print(f"Max tokens: {config.max_tokens:,}")
        print(f"Exceeds limit: {result['exceeds_limit']}")
        print(f"Total tokens: {result['total_tokens']:,}")
        
        if result['exceeds_limit']:
            percentage_over = ((result['total_tokens'] - config.max_tokens) / config.max_tokens) * 100
            print(f"Over limit by: {percentage_over:.1f}%")

def example_batch_processing():
    """Example of batch processing multiple Java modules."""
    print("\n=== Batch Processing Example ===")
    
    # List of Java modules to process
    java_modules = [
        "./src/core",
        "./src/api",
        "./src/services",
        "./src/utils",
        "./src/models"
    ]
    
    # Configuration for batch processing
    config = ContextLimitConfig(
        max_tokens=20000,
        safe_zone_tokens=5000,
        java_file_priority=True,
        include_test_files=False
    )
    
    generator = ACModGenerator(".", context_config=config)
    
    results = []
    
    for module_path_str in java_modules:
        module_path = Path(module_path_str)
        
        if not module_path.exists():
            print(f"⚠️  Module not found: {module_path}")
            continue
        
        print(f"\nProcessing: {module_path}")
        
        # Check context limits
        result = generator.check_module_context_limits(module_path, config.max_tokens)
        
        if result['exceeds_limit']:
            print(f"  ⚠️  Exceeds limit ({result['total_tokens']:,} tokens)")
            success = generator.generate_documentation_with_context_limiting(
                module_path, force=True, max_tokens=config.max_tokens
            )
        else:
            print(f"  ✓ Within limit ({result['total_tokens']:,} tokens)")
            success = generator.generate_documentation(module_path, force=True)
        
        results.append({
            "module": str(module_path),
            "success": success,
            "exceeds_limit": result['exceeds_limit'],
            "total_tokens": result['total_tokens']
        })
    
    # Summary
    print(f"\n=== Batch Processing Summary ===")
    successful = sum(1 for r in results if r['success'])
    total = len(results)
    
    print(f"Processed: {successful}/{total} modules")
    
    for result in results:
        status = "✓" if result['success'] else "✗"
        limit_status = "⚠️" if result['exceeds_limit'] else "✓"
        print(f"  {status} {result['module']} {limit_status} ({result['total_tokens']:,} tokens)")

if __name__ == "__main__":
    print("AC Module Generator - Java Context Limiting Examples")
    print("=" * 60)
    
    # Run examples
    example_basic_usage()
    example_cli_usage()
    example_advanced_configuration()
    example_batch_processing()
    
    print("\n" + "=" * 60)
    print("Examples completed. See the generated .ac.mod.md files in your modules.")
