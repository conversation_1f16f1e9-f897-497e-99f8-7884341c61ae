# python
import openai
from openai import OpenAI
import httpx

openai_api_key = "10333455"
openai_api_base = "http://nebulacoder.dev.zte.com.cn:40081/v1/"
client = OpenAI(
    api_key=openai_api_key,
    base_url=openai_api_base,
    http_client=httpx.Client(trust_env=False)
)

stream = True
# 调用接口
response = client.chat.completions.create(
    model="nebulacoder-v6.0",
    messages=[{"role": "user", "content": "你是谁"}],
    stream=stream,
    max_tokens=1000,
    temperature=0.1,
)

if stream:
    try:
        for chunk in response:
            print(chunk.choices[0].delta.content or "", end="")
        print("")

    except Exception as err:
        print(f"Error: {err}")
else:
    try:
        print(response)
        print("\n\n")
        output = response.choices[0].message.content
        print(f"[Output]\n{output}")
    except Exception as err:
        print(f"Error: {err}")
        