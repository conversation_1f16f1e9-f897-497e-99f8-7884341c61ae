"""
Template Engine for AC Module Documentation

This module provides templating functionality for generating standardized
.ac.mod.md files based on module analysis results.
"""

import os
import re
from typing import Dict, Any, Optional
from pathlib import Path


class TemplateEngine:
    """
    Template engine for generating AC module documentation.
    
    Provides functionality to render standardized .ac.mod.md files
    based on module analysis data.
    """
    
    def __init__(self, template_path: Optional[str] = None):
        """
        Initialize the template engine.
        
        Args:
            template_path: Optional path to custom template file
        """
        self.template_path = template_path
        self.default_template = self._get_default_template()
    
    def render(self, analysis: Dict[str, Any]) -> str:
        """
        Render a complete .ac.mod.md document from analysis data.
        
        Args:
            analysis: Module analysis results
            
        Returns:
            Rendered documentation content
        """
        if self.template_path and os.path.exists(self.template_path):
            with open(self.template_path, 'r', encoding='utf-8') as f:
                template = f.read()
        else:
            template = self.default_template
        
        # Replace template variables
        content = self._render_template(template, analysis)
        return content
    
    def update_existing(self, existing_content: str, analysis: Dict[str, Any]) -> str:
        """
        Update existing documentation while preserving manual modifications.
        
        Args:
            existing_content: Current documentation content
            analysis: Updated module analysis
            
        Returns:
            Updated documentation content
        """
        # For now, we'll do a simple replacement of key sections
        # In the future, this could be more sophisticated
        
        # Update directory structure section
        dir_structure = self._render_directory_structure(analysis.get("directory_structure", {}))
        existing_content = self._replace_section(
            existing_content, 
            "## 目录结构", 
            "## 快速开始", 
            f"## 目录结构\n\n```\n{dir_structure}\n```\n\n"
        )
        
        # Update core components section
        components = self._render_core_components(analysis.get("core_components", []))
        existing_content = self._replace_section(
            existing_content,
            "## 核心组件详解",
            "## 依赖关系说明",
            f"## 核心组件详解\n\n{components}\n"
        )
        
        return existing_content
    
    def _render_template(self, template: str, analysis: Dict[str, Any]) -> str:
        """Render template with analysis data."""
        # Replace basic variables
        content = template.replace("{{MODULE_NAME}}", analysis.get("name", "Unknown Module"))
        content = content.replace("{{MODULE_DESCRIPTION}}", analysis.get("description", "Module description"))
        content = content.replace("{{MODULE_PATH}}", analysis.get("path", ""))
        
        # Render complex sections
        content = content.replace("{{DIRECTORY_STRUCTURE}}", 
                                self._render_directory_structure(analysis.get("directory_structure", {})))
        content = content.replace("{{CORE_COMPONENTS}}", 
                                self._render_core_components(analysis.get("core_components", [])))
        content = content.replace("{{USAGE_EXAMPLES}}", 
                                self._render_usage_examples(analysis.get("usage_examples", [])))
        content = content.replace("{{DEPENDENCIES}}",
                                self._render_dependencies(analysis.get("dependencies", [])))
        content = content.replace("{{TEST_COMMANDS}}",
                                self._render_test_commands(analysis.get("test_commands", [])))
        content = content.replace("{{SUBMODULE_SUMMARIES}}",
                                self._render_submodule_summaries(analysis.get("submodule_summaries", {})))

        return content

    def _render_submodule_summaries(self, submodule_summaries: Dict[str, str]) -> str:
        """Render submodule summaries section."""
        if not submodule_summaries:
            return ""

        content = ["## 子模块概览", ""]
        content.append("本模块包含以下子模块，每个子模块都有独立的详细文档：")
        content.append("")

        # 按路径排序
        sorted_modules = sorted(submodule_summaries.items())

        for rel_path, summary in sorted_modules:
            # 格式化子模块路径
            module_name = rel_path.split('/')[-1]  # 获取最后一级目录名
            doc_path = f"{rel_path}/.ac.mod.md"

            content.append(f"### {module_name}")
            content.append("")
            content.append(f"**路径**: `{rel_path}`")
            content.append(f"**文档**: [{doc_path}]({doc_path})")
            content.append("")
            content.append(f"**功能概述**: {summary}")
            content.append("")

        content.append("💡 **提示**: 点击上述文档链接查看各子模块的详细说明。")
        content.append("")

        return "\n".join(content)
    
    def _render_directory_structure(self, structure) -> str:
        """Render directory structure section."""
        # Handle both old dict format and new list format
        if isinstance(structure, list):
            return self._render_file_list(structure)
        elif isinstance(structure, dict):
            return self._render_tree_structure(structure)
        else:
            return "module/\n└── .ac.mod.md                  # 本文档"

    def _render_file_list(self, file_list: list) -> str:
        """Render directory structure from file list."""
        if not file_list:
            return "module/\n└── .ac.mod.md                  # 本文档"

        # Group files by directory
        dirs = {}
        files = []

        for file_info in file_list:
            path = file_info.get('path', '')
            name = file_info.get('name', '')

            if '/' in path:
                # File in subdirectory
                dir_path = '/'.join(path.split('/')[:-1])
                if dir_path not in dirs:
                    dirs[dir_path] = []
                dirs[dir_path].append(name)
            else:
                # File in root
                files.append(name)

        # Build tree representation
        result = []

        # Add directories first
        for dir_path in sorted(dirs.keys()):
            result.append(f"├── {dir_path}/")
            dir_files = sorted(dirs[dir_path])
            for i, file_name in enumerate(dir_files):
                is_last = (i == len(dir_files) - 1)
                connector = "└── " if is_last else "├── "
                result.append(f"│   {connector}{file_name}")

        # Add root files
        for i, file_name in enumerate(sorted(files)):
            is_last = (i == len(files) - 1) and not dirs
            connector = "└── " if is_last else "├── "
            result.append(f"{connector}{file_name}")

        # Add documentation file
        result.append("└── .ac.mod.md                  # 本文档")

        return "\n".join(result)

    def _render_tree_structure(self, structure: Dict[str, Any]) -> str:
        """Render directory structure from tree format."""
        if not structure:
            return "module/\n└── .ac.mod.md                  # 本文档"

        def render_tree(node: Dict[str, Any], prefix: str = "", is_last: bool = True) -> str:
            result = ""
            name = node.get("name", "")
            description = node.get("description", "")
            
            # Add current node
            connector = "└── " if is_last else "├── "
            line = f"{prefix}{connector}{name}"
            if description:
                line += f"                 # {description}"
            result += line + "\n"
            
            # Add children
            children = node.get("children", [])
            for i, child in enumerate(children):
                is_child_last = (i == len(children) - 1)
                child_prefix = prefix + ("    " if is_last else "│   ")
                result += render_tree(child, child_prefix, is_child_last)
            
            return result
        
        return render_tree(structure).rstrip()
    
    def _render_core_components(self, components: list) -> str:
        """Render core components section."""
        if not components:
            return "### 主要组件\n\n[待分析的核心组件]\n"
        
        result = ""
        for i, component in enumerate(components, 1):
            name = component.get("name", f"Component {i}")
            description = component.get("description", "")
            methods = component.get("methods", [])
            
            result += f"### {i}. {name}\n\n"
            if description:
                result += f"{description}\n\n"
            
            if methods:
                result += "**主要方法:**\n"
                for method in methods:
                    method_name = method.get("name", "")
                    method_desc = method.get("description", "")
                    result += f"- `{method_name}()`: {method_desc}\n"
                result += "\n"
        
        return result.rstrip()
    
    def _render_usage_examples(self, examples: list) -> str:
        """Render usage examples section."""
        if not examples:
            # Check if this might be a Java project by looking for Java-specific patterns
            # This is a simple heuristic - in practice, you might want to pass project type
            return """```java
// 导入必要的类
import {{PACKAGE_NAME}}.[主要类名];

// 1. 创建实例
[主要类名] instance = new [主要类名]();

// 2. 基本使用
[基本使用代码示例]
```

或者对于 Python 项目：

```python
# 导入必要的模块
from {{MODULE_PATH}} import [主要类名]

# 1. 初始化配置
[具体的初始化代码示例]

# 2. 创建实例
[实例创建代码示例]

# 3. 基本使用
[基本使用代码示例]
```"""
        
        result = ""
        for example in examples:
            if example.get("code"):
                result += f"```{example.get('language', 'python')}\n"
                result += example["code"] + "\n"
                result += "```\n\n"
        
        return result.rstrip()
    
    def _render_dependencies(self, dependencies: list) -> str:
        """Render dependencies section."""
        if not dependencies:
            return "暂无依赖其他 AC 模块"
        
        result = ""
        for dep in dependencies:
            result += f"- {dep}\n"
        
        return result.rstrip()
    
    def _render_test_commands(self, commands: list) -> str:
        """Render test commands section."""
        if not commands:
            return "```bash\n# 暂无测试命令\n```"
        
        result = "```bash\n"
        for cmd in commands:
            result += f"{cmd}\n"
        result += "```"
        
        return result
    
    def _replace_section(self, content: str, start_marker: str, end_marker: str, new_content: str) -> str:
        """Replace a section in existing content."""
        pattern = f"({re.escape(start_marker)}.*?)({re.escape(end_marker)})"
        replacement = f"{new_content}{end_marker}"
        
        if re.search(pattern, content, re.DOTALL):
            return re.sub(pattern, replacement, content, flags=re.DOTALL)
        else:
            # If section doesn't exist, append it
            return content + "\n\n" + new_content
    
    def _get_default_template(self) -> str:
        """Get the default template for .ac.mod.md files."""
        return """# {{MODULE_NAME}}

{{MODULE_DESCRIPTION}}

## 目录结构

```
{{DIRECTORY_STRUCTURE}}
```

## 快速开始

### 基本使用方式

{{USAGE_EXAMPLES}}

### 辅助函数说明

[详细说明模块提供的辅助函数]

### 配置管理

[说明配置选项和管理方式]

## 核心组件详解

{{CORE_COMPONENTS}}

{{SUBMODULE_SUMMARIES}}

## 依赖关系说明

{{DEPENDENCIES}}

## 可以验证模块可运行的测试命令

{{TEST_COMMANDS}}
"""
