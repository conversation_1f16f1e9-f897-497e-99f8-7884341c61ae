#!/usr/bin/env python3
"""
Basic functionality test for AC Module Generator with LLM integration
"""

import os
import tempfile
import unittest
from pathlib import Path
from unittest.mock import patch, Mock

from generator import ACModGenerator
from llm_config import LLMConfig, LLMClient


class TestBasicFunctionality(unittest.TestCase):
    """Test basic functionality of the AC Module Generator"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_module_path = Path(self.temp_dir) / "test_module"
        self.test_module_path.mkdir()
        
        # Create a simple Python file
        (self.test_module_path / "__init__.py").write_text(
            '"""Test module for basic functionality"""\n'
        )
        (self.test_module_path / "main.py").write_text(
            'def hello():\n    """Say hello"""\n    return "Hello, World!"\n'
        )
    
    def test_template_based_generation(self):
        """Test template-based documentation generation (no LLM)"""
        generator = ACModGenerator(root_path=self.temp_dir)
        
        # Generate documentation
        success = generator.generate_documentation(self.test_module_path, force=True)
        self.assertTrue(success)
        
        # Check if documentation file was created
        doc_path = self.test_module_path / ".ac.mod.md"
        self.assertTrue(doc_path.exists())
        
        # Check content
        content = doc_path.read_text()
        self.assertIn("test_module", content)
        self.assertIn("目录结构", content)
    
    def test_llm_config_creation(self):
        """Test LLM configuration creation"""
        config = LLMConfig(
            api_base="https://api.openai.com/v1",
            api_key="test_key",
            model="gpt-3.5-turbo"
        )
        
        self.assertEqual(config.api_base, "https://api.openai.com/v1")
        self.assertEqual(config.api_key, "test_key")
        self.assertEqual(config.model, "gpt-3.5-turbo")
    
    def test_generator_with_llm_config(self):
        """Test generator initialization with LLM config"""
        llm_config = LLMConfig(api_key="test_key")
        generator = ACModGenerator(
            root_path=self.temp_dir,
            llm_config=llm_config
        )
        
        self.assertTrue(generator.llm_client.is_available())
    
    def test_runtime_llm_configuration(self):
        """Test setting LLM configuration at runtime"""
        generator = ACModGenerator(root_path=self.temp_dir)
        self.assertFalse(generator.llm_client.is_available())
        
        # Set LLM config at runtime
        generator.set_llm_config(
            api_base="https://api.openai.com/v1",
            api_key="test_key",
            model="gpt-3.5-turbo"
        )
        
        self.assertTrue(generator.llm_client.is_available())
        self.assertEqual(generator.llm_client.config.api_base, "https://api.openai.com/v1")
        self.assertEqual(generator.llm_client.config.api_key, "test_key")
        self.assertEqual(generator.llm_client.config.model, "gpt-3.5-turbo")
    
    @patch.object(LLMClient, 'generate_documentation')
    def test_llm_generation_success(self, mock_generate):
        """Test successful LLM documentation generation"""
        mock_generate.return_value = "# AI Generated Documentation\n\nThis is AI-generated content."
        
        llm_config = LLMConfig(api_key="test_key")
        generator = ACModGenerator(
            root_path=self.temp_dir,
            llm_config=llm_config
        )
        
        # Generate documentation
        success = generator.generate_documentation(self.test_module_path, force=True)
        self.assertTrue(success)
        
        # Check if documentation file was created
        doc_path = self.test_module_path / ".ac.mod.md"
        self.assertTrue(doc_path.exists())
        
        # Check content is from LLM
        content = doc_path.read_text()
        self.assertIn("AI Generated Documentation", content)
        self.assertIn("AI-generated content", content)
    
    @patch.object(LLMClient, 'generate_documentation')
    def test_llm_generation_fallback(self, mock_generate):
        """Test fallback to template when LLM fails"""
        mock_generate.return_value = None  # LLM generation failed
        
        llm_config = LLMConfig(api_key="test_key")
        generator = ACModGenerator(
            root_path=self.temp_dir,
            llm_config=llm_config
        )
        
        # Generate documentation
        success = generator.generate_documentation(self.test_module_path, force=True)
        self.assertTrue(success)
        
        # Check if documentation file was created
        doc_path = self.test_module_path / ".ac.mod.md"
        self.assertTrue(doc_path.exists())
        
        # Check content is from template (not LLM)
        content = doc_path.read_text()
        self.assertIn("test_module", content)
        self.assertNotIn("AI Generated", content)
    
    def test_environment_variable_config(self):
        """Test configuration from environment variables"""
        with patch.dict(os.environ, {
            'OPENAI_API_BASE': 'https://test.api.com/v1',
            'OPENAI_API_KEY': 'env_test_key',
            'OPENAI_MODEL': 'gpt-4',
        }):
            config = LLMConfig()
            self.assertEqual(config.api_base, "https://test.api.com/v1")
            self.assertEqual(config.api_key, "env_test_key")
            self.assertEqual(config.model, "gpt-4")
    
    def test_module_discovery(self):
        """Test module discovery functionality"""
        generator = ACModGenerator(root_path=self.temp_dir)
        modules = generator.discover_modules()
        
        # Should find our test module
        self.assertGreater(len(modules), 0)
        self.assertIn(self.test_module_path, modules)
    
    def test_module_analysis(self):
        """Test module analysis functionality"""
        generator = ACModGenerator(root_path=self.temp_dir)
        analysis = generator.analyze_module(self.test_module_path)
        
        self.assertEqual(analysis["name"], "test_module")
        self.assertIn("test_module", analysis["path"])
        self.assertIsInstance(analysis["core_components"], list)
        self.assertIsInstance(analysis["dependencies"], list)


class TestLLMClientBasics(unittest.TestCase):
    """Test basic LLM client functionality"""
    
    def test_client_availability_without_key(self):
        """Test client availability without API key"""
        config = LLMConfig(api_key=None)
        client = LLMClient(config)
        self.assertFalse(client.is_available())
    
    def test_client_availability_with_key(self):
        """Test client availability with API key"""
        config = LLMConfig(api_key="test_key")
        client = LLMClient(config)
        self.assertTrue(client.is_available())
    
    def test_prompt_generation(self):
        """Test documentation prompt generation"""
        config = LLMConfig(api_key="test_key")
        client = LLMClient(config)
        
        module_info = {
            "name": "test_module",
            "path": "./test_module",
            "description": "A test module",
            "core_components": [
                {"name": "TestClass", "type": "class", "description": "Test class"}
            ]
        }
        
        prompt = client._create_documentation_prompt(module_info)
        self.assertIn("test_module", prompt)
        self.assertIn("TestClass", prompt)
        self.assertIn("请为以下模块生成", prompt)


if __name__ == "__main__":
    unittest.main()
