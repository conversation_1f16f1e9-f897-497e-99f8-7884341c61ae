"""
Test script for context limiting functionality in AC Module Generator

This script demonstrates and tests the context limiting features for large Java modules.
"""

import os
import tempfile
import shutil
from pathlib import Path
from context_limiter import ContextLimiter, ContextLimitConfig
from generator import ACModGenerator

def create_test_java_module(base_path: Path, num_files: int = 20) -> Path:
    """Create a test Java module with many files to test context limiting."""
    module_path = base_path / "test_large_java_module"
    module_path.mkdir(exist_ok=True)
    
    # Create src/main/java structure
    java_src_path = module_path / "src" / "main" / "java" / "com" / "example"
    java_src_path.mkdir(parents=True, exist_ok=True)
    
    # Create pom.xml
    pom_content = """<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.example</groupId>
    <artifactId>test-large-module</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>
    
    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
    </properties>
</project>"""
    
    with open(module_path / "pom.xml", "w") as f:
        f.write(pom_content)
    
    # Create many Java files
    for i in range(num_files):
        java_content = f"""package com.example;

/**
 * This is a test class number {i} for demonstrating context limiting.
 * It contains various methods and properties to simulate a real Java class.
 * This class is part of a large module that might exceed context limits.
 */
public class TestClass{i:02d} {{
    
    private String name;
    private int value;
    private boolean active;
    
    /**
     * Constructor for TestClass{i:02d}
     * @param name The name parameter
     * @param value The value parameter
     */
    public TestClass{i:02d}(String name, int value) {{
        this.name = name;
        this.value = value;
        this.active = true;
    }}
    
    /**
     * Gets the name property
     * @return The name value
     */
    public String getName() {{
        return name;
    }}
    
    /**
     * Sets the name property
     * @param name The new name value
     */
    public void setName(String name) {{
        this.name = name;
    }}
    
    /**
     * Gets the value property
     * @return The value
     */
    public int getValue() {{
        return value;
    }}
    
    /**
     * Sets the value property
     * @param value The new value
     */
    public void setValue(int value) {{
        this.value = value;
    }}
    
    /**
     * Checks if the object is active
     * @return true if active, false otherwise
     */
    public boolean isActive() {{
        return active;
    }}
    
    /**
     * Sets the active status
     * @param active The new active status
     */
    public void setActive(boolean active) {{
        this.active = active;
    }}
    
    /**
     * Processes the data in this class
     * This method demonstrates some business logic
     */
    public void processData() {{
        if (active && value > 0) {{
            System.out.println("Processing data for " + name + " with value " + value);
            // Simulate some processing
            for (int j = 0; j < value; j++) {{
                // Do some work
                String result = "Processed item " + j + " for " + name;
                if (j % 10 == 0) {{
                    System.out.println(result);
                }}
            }}
        }}
    }}
    
    /**
     * Validates the current state of the object
     * @return true if valid, false otherwise
     */
    public boolean validate() {{
        return name != null && !name.isEmpty() && value >= 0;
    }}
    
    /**
     * Returns a string representation of this object
     * @return String representation
     */
    @Override
    public String toString() {{
        return "TestClass{i:02d}{{" +
                "name='" + name + '\'' +
                ", value=" + value +
                ", active=" + active +
                '}}';
    }}
    
    /**
     * Checks equality with another object
     * @param obj The object to compare with
     * @return true if equal, false otherwise
     */
    @Override
    public boolean equals(Object obj) {{
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        TestClass{i:02d} that = (TestClass{i:02d}) obj;
        return value == that.value &&
                active == that.active &&
                name.equals(that.name);
    }}
    
    /**
     * Generates hash code for this object
     * @return The hash code
     */
    @Override
    public int hashCode() {{
        return name.hashCode() + value * 31 + (active ? 1 : 0);
    }}
}}"""
        
        with open(java_src_path / f"TestClass{i:02d}.java", "w") as f:
            f.write(java_content)
    
    # Create a main application class
    main_content = """package com.example;

/**
 * Main application class for the test large Java module.
 * This class demonstrates the entry point of the application.
 */
public class Application {
    
    public static void main(String[] args) {
        System.out.println("Starting Test Large Java Module Application");
        
        // Create some test instances
        for (int i = 0; i < 5; i++) {
            TestClass01 test = new TestClass01("Test " + i, i * 10);
            test.processData();
            System.out.println(test.toString());
        }
        
        System.out.println("Application completed successfully");
    }
}"""
    
    with open(java_src_path / "Application.java", "w") as f:
        f.write(main_content)
    
    # Create README
    readme_content = f"""# Test Large Java Module

This is a test module with {num_files + 1} Java files to demonstrate context limiting functionality.

## Structure

- `src/main/java/com/example/` - Contains {num_files} test classes and 1 main application class
- `pom.xml` - Maven configuration file

## Purpose

This module is designed to exceed typical context window limits to test the AC Module Generator's
context limiting capabilities for large Java projects.

## Build

```bash
mvn clean compile
mvn exec:java -Dexec.mainClass="com.example.Application"
```
"""
    
    with open(module_path / "README.md", "w") as f:
        f.write(readme_content)
    
    return module_path

def test_context_limiting():
    """Test the context limiting functionality."""
    print("Testing Context Limiting Functionality")
    print("=" * 50)
    
    # Create temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Create test module with many files
        print("Creating test Java module with 30 files...")
        module_path = create_test_java_module(temp_path, num_files=30)
        
        # Test context analysis
        print(f"\nAnalyzing module: {module_path}")
        
        # Test with different token limits
        for max_tokens in [8000, 16000, 32000]:
            print(f"\n--- Testing with {max_tokens:,} token limit ---")
            
            config = ContextLimitConfig(max_tokens=max_tokens)
            limiter = ContextLimiter(config)
            
            analysis = limiter.analyze_module(module_path)
            summary = limiter.get_context_summary(analysis)
            
            print(summary)
            
            if analysis.exceeds_limit:
                print(f"✓ Correctly detected context limit exceeded")
                print(f"  Recommended strategy: {analysis.recommended_strategy}")
                
                # Test limiting
                conversations = [{"role": "user", "content": "Generate documentation for this Java module"}]
                limited_sources, _ = limiter.limit_module_content(module_path, conversations)
                
                limited_tokens = sum(source.tokens for source in limited_sources)
                print(f"  After limiting: {len(limited_sources)} files, {limited_tokens:,} tokens")
            else:
                print(f"✓ Module is within {max_tokens:,} token limit")
        
        # Test with AC Module Generator
        print(f"\n--- Testing with AC Module Generator ---")
        
        generator = ACModGenerator(temp_path)
        
        # Check context limits
        result = generator.check_module_context_limits(module_path, max_tokens=16000)
        print(f"Generator context check result:")
        print(f"  Exceeds limit: {result['exceeds_limit']}")
        print(f"  Total files: {result['total_files']}")
        print(f"  Total tokens: {result['total_tokens']:,}")
        
        # Generate documentation with context limiting
        print(f"\nGenerating documentation with context limiting...")
        success = generator.generate_documentation_with_context_limiting(
            module_path, force=True, max_tokens=16000
        )
        
        if success:
            doc_path = module_path / ".ac.mod.md"
            if doc_path.exists():
                print(f"✓ Documentation generated successfully: {doc_path}")
                
                # Check if context limiting note was added
                content = doc_path.read_text()
                if "Context Limiting Information" in content:
                    print("✓ Context limiting information included in documentation")
                else:
                    print("ℹ No context limiting information (module may not have exceeded limits)")
            else:
                print("✗ Documentation file not found")
        else:
            print("✗ Documentation generation failed")

if __name__ == "__main__":
    test_context_limiting()
