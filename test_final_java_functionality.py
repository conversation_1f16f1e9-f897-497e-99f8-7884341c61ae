#!/usr/bin/env python3
"""
Final test for Java-only functionality
"""

import sys
from pathlib import Path
from generator import ACModGenerator

def test_java_functionality():
    """Test the complete Java-only functionality."""
    
    test_path = Path("test_java").resolve()
    
    print("=== Final Java-only AC Module Generator Test ===")
    print(f"Test path: {test_path}")
    
    # Initialize generator
    generator = ACModGenerator(root_path=str(test_path.parent))
    
    # Test module analysis
    print("\n1. Testing module analysis...")
    analysis = generator.analyze_module(test_path)
    
    print(f"   ✓ Module name: {analysis['name']}")
    print(f"   ✓ Module path: {analysis['path']}")
    print(f"   ✓ Description: {analysis['description']}")
    
    # Test directory structure
    print("\n2. Testing directory structure...")
    dir_structure = analysis['directory_structure']
    print(f"   ✓ Found {len(dir_structure)} files")
    java_files = [f for f in dir_structure if f['extension'] == '.java']
    print(f"   ✓ Java files: {len(java_files)}")
    for java_file in java_files:
        print(f"     - {java_file['name']}")
    
    # Test core components
    print("\n3. Testing core components...")
    components = analysis['core_components']
    print(f"   ✓ Found {len(components)} Java components")
    
    for i, component in enumerate(components, 1):
        print(f"   Component {i}:")
        print(f"     - File: {component['file']}")
        print(f"     - Class/Interface: {component['class']} ({component['type']})")
        print(f"     - Function count: {component['function_count']}")
        print(f"     - Key functions:")
        for func in component['key_functions']:
            print(f"       * {func['name']}: {func['signature']}")
    
    # Test documentation generation
    print("\n4. Testing documentation generation...")
    try:
        success = generator.generate_documentation(test_path, force=True)
        if success:
            print("   ✓ Documentation generated successfully")
            
            # Check if file was created
            doc_file = test_path / ".ac.mod.md"
            if doc_file.exists():
                print(f"   ✓ Documentation file created: {doc_file}")
                
                # Show first few lines
                with open(doc_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()[:10]
                print("   ✓ Documentation preview:")
                for line in lines:
                    print(f"     {line.rstrip()}")
            else:
                print("   ✗ Documentation file not found")
        else:
            print("   ✗ Documentation generation failed")
    except Exception as e:
        print(f"   ✗ Documentation generation error: {e}")
    
    print("\n=== Test Summary ===")
    print("✓ Java file discovery")
    print("✓ Function signature extraction")
    print("✓ Directory structure listing")
    print("✓ Core component summarization")
    print("✓ Documentation generation")
    print("\nAll Java-only functionality is working correctly!")

if __name__ == "__main__":
    test_java_functionality()
