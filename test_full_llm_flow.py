#!/usr/bin/env python3
"""
测试完整的LLM文档生成流程
"""

import sys
import logging
from pathlib import Path
from generate_java_doc import generate_java_documentation, create_llm_config
from llm_config import LLMConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class MockArgs:
    """模拟命令行参数"""
    def __init__(self):
        self.api_base = "http://10.55.57.164:30804/qwen3-235b-fp8-openai-server/v1/v1"
        self.api_key = "10298467"
        self.model = "/Qwen2-235B-A22B-FP8"

def test_llm_config_creation():
    """测试LLM配置创建"""
    print("🔧 测试LLM配置创建")
    print("=" * 50)
    
    # 模拟命令行参数
    args = MockArgs()
    
    print(f"📋 模拟参数:")
    print(f"   API Base: {args.api_base}")
    print(f"   API Key: {args.api_key}")
    print(f"   Model: {args.model}")
    
    # 创建LLM配置
    llm_config = create_llm_config(args)
    
    if llm_config:
        print("✅ LLM配置创建成功")
        print(f"   配置类型: {type(llm_config)}")
        print(f"   API Base: {llm_config.api_base}")
        print(f"   API Key: {llm_config.api_key}")
        print(f"   Model: {llm_config.model}")
        return llm_config
    else:
        print("❌ LLM配置创建失败")
        return None

def test_direct_llm_call():
    """测试直接LLM调用"""
    print(f"\n🤖 测试直接LLM调用")
    print("=" * 50)
    
    # 创建配置
    config = LLMConfig(
        api_base="http://10.55.57.164:30804/qwen3-235b-fp8-openai-server/v1/v1",
        api_key="10298467",
        model="/Qwen2-235B-A22B-FP8"
    )
    
    from llm_config import LLMClient
    client = LLMClient(config)
    
    print(f"✅ 客户端可用: {client.is_available()}")
    
    # 简单测试
    messages = [
        {"role": "user", "content": "请用中文说'Hello World'"}
    ]
    
    try:
        response = client.chat_completion(messages)
        if response:
            print(f"✅ LLM响应成功: {response}")
            return True
        else:
            print("❌ LLM响应为空")
            return False
    except Exception as e:
        print(f"❌ LLM调用失败: {e}")
        return False

def test_generator_with_llm():
    """测试生成器的LLM功能"""
    print(f"\n📝 测试生成器的LLM功能")
    print("=" * 50)
    
    # 使用默认测试路径
    test_path = Path("/home/<USER>/fengbin/DailyWork/ZeroAgents/test_repo/PATCHER")
    
    if not test_path.exists():
        print(f"❌ 测试路径不存在: {test_path}")
        return False
    
    print(f"📂 测试路径: {test_path}")
    
    # 创建LLM配置
    llm_config = LLMConfig(
        api_base="http://10.55.57.164:30804/qwen3-235b-fp8-openai-server/v1/v1",
        api_key="10298467",
        model="/Qwen2-235B-A22B-FP8"
    )
    
    from generator import ACModGenerator
    generator = ACModGenerator(root_path=test_path.parent, llm_config=llm_config)
    
    print(f"✅ 生成器LLM可用: {generator.llm_client.is_available()}")
    
    # 分析模块
    analysis = generator.analyze_module(test_path)
    print(f"📊 模块分析完成: {analysis['name']}")
    
    # 测试内容生成
    print("🤖 测试LLM内容生成...")
    content = generator._generate_content(analysis)
    
    if content:
        print(f"✅ 内容生成成功")
        print(f"📄 内容长度: {len(content)} 字符")
        
        # 检查是否是LLM生成的内容
        if "{{" in content:
            print("❌ 内容包含模板变量，可能是模板生成")
            return False
        else:
            print("✅ 内容不包含模板变量，可能是LLM生成")
            
            # 保存测试文件
            test_file = test_path / ".ac.mod.md.llm_test"
            test_file.write_text(content, encoding='utf-8')
            print(f"💾 测试文件已保存: {test_file}")
            return True
    else:
        print("❌ 内容生成失败")
        return False

def main():
    """主函数"""
    print("🚀 完整LLM流程测试")
    print("=" * 60)
    
    # 1. 测试配置创建
    llm_config = test_llm_config_creation()
    if not llm_config:
        print("\n❌ 配置创建失败，停止测试")
        return
    
    # 2. 测试直接LLM调用
    llm_success = test_direct_llm_call()
    if not llm_success:
        print("\n❌ 直接LLM调用失败，停止测试")
        return
    
    # 3. 测试生成器LLM功能
    generator_success = test_generator_with_llm()
    
    print(f"\n{'='*60}")
    if generator_success:
        print("✅ 所有测试通过! LLM文档生成功能正常")
        print("\n💡 现在可以确认:")
        print("   1. LLM配置正确")
        print("   2. LLM API调用正常")
        print("   3. 生成器能够使用LLM生成内容")
        print("   4. 生成的内容不是模板内容")
    else:
        print("❌ 生成器LLM功能测试失败")
        print("\n🔧 可能的问题:")
        print("   1. 生成器没有正确使用LLM配置")
        print("   2. _generate_content方法有问题")
        print("   3. LLM生成的内容被模板覆盖")

if __name__ == "__main__":
    main()
