#!/usr/bin/env python3
"""
测试强制分层文档生成功能
"""

import sys
from pathlib import Path
from generate_java_doc import generate_java_documentation_hierarchical
from llm_config import LLMConfig

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("使用方法:")
        print("  python test_hierarchical_force.py <java_module_path>")
        print("\n示例:")
        print("  python test_hierarchical_force.py /path/to/daip-patcher-service")
        print("\n说明:")
        print("  - 这个脚本会强制为指定模块使用分层文档生成")
        print("  - 每个包含Java文件的子目录都会生成独立的.ac.mod.md文件")
        sys.exit(1)
    
    module_path = sys.argv[1]
    
    # 创建LLM配置（使用默认配置）
    llm_config = LLMConfig(
        api_base="http://************:30804/qwen3-235b-fp8-openai-server/v1/v1",
        api_key="10298467",
        model="/Qwen2-235B-A22B-FP8"
    )
    
    print("🚀 开始强制分层文档生成测试")
    print(f"📂 目标模块: {module_path}")
    print("⚡ 模式: 强制分层生成（所有子包都会生成文档）")
    print()
    
    # 执行强制分层文档生成
    success = generate_java_documentation_hierarchical(module_path, llm_config)
    
    if success:
        print("\n✅ 强制分层文档生成测试完成!")
        print("\n📋 检查结果:")
        print("   1. 查看主模块目录下的 .ac.mod.md 文件")
        print("   2. 查看各个子包目录下的 .ac.mod.md 文件")
        print("   3. 确认每个包含Java文件的子目录都有对应的文档")
    else:
        print("\n❌ 强制分层文档生成失败!")
        sys.exit(1)

if __name__ == "__main__":
    main()
