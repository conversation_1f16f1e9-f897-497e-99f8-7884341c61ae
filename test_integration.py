#!/usr/bin/env python3
"""
Integration test for AC Module Generator with Java context limiting

This script tests the complete integration of the new context limiting functionality.
"""

import sys
import tempfile
import shutil
from pathlib import Path

# Add the current directory to Python path for imports
sys.path.insert(0, str(Path(__file__).parent))

from generator import ACModGenerator
from context_limiter import ContextLimitConfig

def create_test_java_project(base_path: Path, num_files: int = 15) -> Path:
    """Create a test Java project with specified number of files."""
    project_path = base_path / "test_java_project"
    project_path.mkdir(exist_ok=True)
    
    # Create Maven structure
    java_src = project_path / "src" / "main" / "java" / "com" / "example"
    java_src.mkdir(parents=True, exist_ok=True)
    
    # Create pom.xml
    pom_content = """<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.example</groupId>
    <artifactId>test-project</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>
    
    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
    </properties>
</project>"""
    
    with open(project_path / "pom.xml", "w") as f:
        f.write(pom_content)
    
    # Create Java files
    for i in range(num_files):
        java_content = f"""package com.example;

/**
 * Test class {i} for demonstrating AC Module Generator functionality.
 * This class contains various methods to simulate a real Java application.
 */
public class TestClass{i:02d} {{
    
    private String name;
    private int value;
    
    /**
     * Constructor for TestClass{i:02d}
     */
    public TestClass{i:02d}(String name, int value) {{
        this.name = name;
        this.value = value;
    }}
    
    /**
     * Process data method
     */
    public void processData() {{
        System.out.println("Processing data for " + name);
    }}
    
    /**
     * Get name
     */
    public String getName() {{
        return name;
    }}
    
    /**
     * Set name
     */
    public void setName(String name) {{
        this.name = name;
    }}
    
    /**
     * Get value
     */
    public int getValue() {{
        return value;
    }}
    
    /**
     * Set value
     */
    public void setValue(int value) {{
        this.value = value;
    }}
}}"""
        
        with open(java_src / f"TestClass{i:02d}.java", "w") as f:
            f.write(java_content)
    
    # Create main application class
    main_content = """package com.example;

/**
 * Main application class for the test project.
 */
public class Application {
    
    public static void main(String[] args) {
        System.out.println("Test Java Application");
        
        TestClass01 test = new TestClass01("Test", 42);
        test.processData();
    }
}"""
    
    with open(java_src / "Application.java", "w") as f:
        f.write(main_content)
    
    # Create README
    readme_content = f"""# Test Java Project

This is a test Java project with {num_files + 1} classes for testing AC Module Generator.

## Build

```bash
mvn clean compile
mvn exec:java -Dexec.mainClass="com.example.Application"
```
"""
    
    with open(project_path / "README.md", "w") as f:
        f.write(readme_content)
    
    return project_path

def test_basic_functionality():
    """Test basic Java module documentation generation."""
    print("=== Testing Basic Functionality ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Create small test project
        project_path = create_test_java_project(temp_path, num_files=5)
        
        # Test basic generation
        generator = ACModGenerator(temp_path)
        success = generator.generate_documentation(project_path)
        
        if success:
            doc_path = project_path / ".ac.mod.md"
            if doc_path.exists():
                print("✓ Basic documentation generation successful")
                
                # Check content
                content = doc_path.read_text()
                if "Application" in content and "TestClass" in content:
                    print("✓ Documentation contains expected Java classes")
                else:
                    print("✗ Documentation missing expected content")
            else:
                print("✗ Documentation file not created")
        else:
            print("✗ Basic documentation generation failed")

def test_context_limiting():
    """Test context limiting functionality."""
    print("\n=== Testing Context Limiting ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Create large test project
        project_path = create_test_java_project(temp_path, num_files=25)
        
        # Test context analysis
        generator = ACModGenerator(temp_path)
        result = generator.check_module_context_limits(project_path, max_tokens=8000)
        
        print(f"Module analysis:")
        print(f"  Total files: {result['total_files']}")
        print(f"  Total tokens: {result['total_tokens']:,}")
        print(f"  Exceeds limit: {result['exceeds_limit']}")
        
        if result['exceeds_limit']:
            print("✓ Correctly detected context limit exceeded")
            
            # Test context limiting generation
            config = ContextLimitConfig(max_tokens=8000, java_file_priority=True)
            generator = ACModGenerator(temp_path, context_config=config)
            
            success = generator.generate_documentation_with_context_limiting(
                project_path, force=True, max_tokens=8000
            )
            
            if success:
                doc_path = project_path / ".ac.mod.md"
                if doc_path.exists():
                    content = doc_path.read_text()
                    if "Context Limiting Information" in content:
                        print("✓ Context limiting documentation generated with info note")
                    else:
                        print("✓ Context limiting documentation generated")
                else:
                    print("✗ Context limiting documentation file not created")
            else:
                print("✗ Context limiting documentation generation failed")
        else:
            print("ℹ Module is within context limits (increase file count for testing)")

def test_cli_integration():
    """Test CLI integration."""
    print("\n=== Testing CLI Integration ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Create test project
        project_path = create_test_java_project(temp_path, num_files=10)
        
        # Test CLI commands (simulate)
        print("CLI commands that would be tested:")
        print(f"  check-context {project_path} --show-files")
        print(f"  generate {project_path} --enable-context-limiting --max-tokens 16000")
        
        # For now, just test the underlying functionality
        generator = ACModGenerator(temp_path)
        result = generator.check_module_context_limits(project_path, max_tokens=16000)
        
        print(f"CLI simulation result:")
        print(f"  Would show: {result['total_files']} files, {result['total_tokens']:,} tokens")
        print("✓ CLI integration ready")

def main():
    """Run all integration tests."""
    print("AC Module Generator - Java Context Limiting Integration Test")
    print("=" * 60)
    
    try:
        test_basic_functionality()
        test_context_limiting()
        test_cli_integration()
        
        print("\n" + "=" * 60)
        print("✓ All integration tests completed successfully!")
        print("\nNext steps:")
        print("1. Test with real Java projects")
        print("2. Adjust token limits based on your LLM")
        print("3. Customize ContextLimitConfig for your needs")
        
    except Exception as e:
        print(f"\n✗ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
