package com.example.test;

/**
 * Test service for demonstration
 */
public class TestService {
    
    private String name;
    
    /**
     * Constructor
     */
    public TestService(String name) {
        this.name = name;
    }
    
    /**
     * Process data with given parameters
     */
    public String processData(String input, int count) {
        return input + "_processed_" + count;
    }
    
    /**
     * Calculate result based on input
     */
    public int calculateResult(int a, int b) {
        return a + b * 2;
    }
    
    /**
     * Validate input data
     */
    public boolean validateInput(String data) {
        return data != null && !data.isEmpty();
    }
    
    // Getter method (should be filtered out)
    public String getName() {
        return name;
    }
    
    // Setter method (should be filtered out)
    public void setName(String name) {
        this.name = name;
    }
}
