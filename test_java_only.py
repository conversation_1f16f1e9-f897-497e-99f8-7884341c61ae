#!/usr/bin/env python3
"""
Test script for Java-only functionality
"""

import sys
import logging
from pathlib import Path
from generator import ACModGenerator

# Set up logging
logging.basicConfig(level=logging.INFO)

def test_java_only_functionality():
    """Test the modified Java-only functionality."""

    # Test both current directory and test_java directory
    test_paths = [Path(".").resolve(), Path("test_java").resolve()]

    for test_path in test_paths:
        print(f"\n{'='*50}")
        print(f"Testing Java-only AC Module Generator...")
        print(f"Test path: {test_path}")

        # Initialize generator
        generator = ACModGenerator(root_path=str(test_path.parent))

        # Test module analysis
        print("\n=== Testing module analysis ===")
        analysis = generator.analyze_module(test_path)
    
        print(f"Module name: {analysis['name']}")
        print(f"Module path: {analysis['path']}")
        print(f"Description: {analysis['description']}")

        print(f"\n=== Directory structure ===")
        print(f"Found {len(analysis['directory_structure'])} files")
        for file_info in analysis['directory_structure'][:5]:  # Show first 5 files
            print(f"  - {file_info['name']} ({file_info['extension']})")

        print(f"\n=== Core components ===")
        print(f"Found {len(analysis['core_components'])} Java files with components")
        for component in analysis['core_components'][:3]:  # Show first 3 components
            print(f"  - File: {component['file']}")
            print(f"    Class: {component.get('class', 'N/A')}")
            print(f"    Functions: {component['function_count']}")
            if component['key_functions']:
                for func in component['key_functions'][:2]:  # Show first 2 functions
                    print(f"      * {func['name']}")

    print("\n=== Test completed ===")

if __name__ == "__main__":
    test_java_only_functionality()
