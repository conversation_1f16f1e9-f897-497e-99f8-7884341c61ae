#!/usr/bin/env python3
"""
测试LLM文档生成功能
"""

import sys
from pathlib import Path
from generator import ACModGenerator
from llm_config import LLMConfig

def test_llm_generation(module_path_str: str):
    """
    测试LLM文档生成功能
    
    Args:
        module_path_str: 模块路径字符串
    """
    module_path = Path(module_path_str).resolve()
    
    print(f"🤖 测试LLM文档生成功能")
    print(f"📂 模块路径: {module_path}")
    
    if not module_path.exists():
        print(f"❌ 错误: 路径不存在 - {module_path}")
        return False
    
    # 创建LLM配置
    llm_config = LLMConfig(
        api_base="http://10.55.57.164:30804/qwen3-235b-fp8-openai-server/v1/v1",
        api_key="10298467",
        model="/Qwen2-235B-A22B-FP8"
    )
    
    print(f"🔧 LLM配置:")
    print(f"   API Base: {llm_config.api_base}")
    print(f"   API Key: {llm_config.api_key[:8]}...{llm_config.api_key[-4:]}")
    print(f"   Model: {llm_config.model}")
    
    # 创建生成器并配置LLM
    generator = ACModGenerator(root_path=module_path.parent, llm_config=llm_config)
    
    # 检查LLM是否可用
    if generator.llm_client.is_available():
        print("✅ LLM客户端可用")
    else:
        print("❌ LLM客户端不可用")
        return False
    
    # 测试单个模块的LLM生成
    print(f"\n🚀 开始LLM文档生成...")
    
    try:
        # 分析模块
        print("📊 分析模块...")
        analysis = generator.analyze_module(module_path)
        
        print(f"   模块名称: {analysis['name']}")
        print(f"   核心组件: {len(analysis['core_components'])} 个")
        print(f"   依赖关系: {len(analysis['dependencies'])} 个")
        
        # 测试LLM内容生成
        print("🤖 调用LLM生成内容...")
        content = generator._generate_content(analysis)
        
        if content:
            print("✅ LLM生成成功")
            print(f"📄 生成内容长度: {len(content)} 字符")
            
            # 检查内容特征
            if "# " in content:
                print("   ✅ 包含标题")
            if "## " in content:
                print("   ✅ 包含二级标题")
            if "```" in content:
                print("   ✅ 包含代码块")
            
            # 显示前几行内容
            lines = content.split('\n')
            print("📋 生成内容预览:")
            for i, line in enumerate(lines[:10]):
                if line.strip():
                    print(f"   {i+1}: {line}")
            if len(lines) > 10:
                print("   ...")
            
            # 保存到临时文件
            temp_file = module_path / ".ac.mod.md.test"
            temp_file.write_text(content, encoding='utf-8')
            print(f"💾 测试文档已保存: {temp_file}")
            
            return True
        else:
            print("❌ LLM生成失败，可能回退到模板")
            return False
            
    except Exception as e:
        print(f"❌ 生成过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_template_vs_llm_comparison(module_path_str: str):
    """
    对比模板生成和LLM生成的差异
    """
    module_path = Path(module_path_str).resolve()
    
    print(f"\n🔄 对比模板生成 vs LLM生成")
    print("=" * 50)
    
    # 1. 模板生成
    print("📝 模板生成:")
    generator_template = ACModGenerator(root_path=module_path.parent)  # 无LLM配置
    analysis = generator_template.analyze_module(module_path)
    template_content = generator_template._generate_content(analysis)
    
    print(f"   长度: {len(template_content)} 字符")
    print(f"   行数: {len(template_content.split('\n'))} 行")
    
    # 2. LLM生成
    print("🤖 LLM生成:")
    llm_config = LLMConfig(
        api_base="http://10.55.57.164:30804/qwen3-235b-fp8-openai-server/v1/v1",
        api_key="10298467",
        model="/Qwen2-235B-A22B-FP8"
    )
    generator_llm = ACModGenerator(root_path=module_path.parent, llm_config=llm_config)
    llm_content = generator_llm._generate_content(analysis)
    
    print(f"   长度: {len(llm_content)} 字符")
    print(f"   行数: {len(llm_content.split('\n'))} 行")
    
    # 3. 对比分析
    print("\n📊 对比分析:")
    if len(llm_content) > len(template_content):
        print(f"   ✅ LLM生成内容更丰富 (+{len(llm_content) - len(template_content)} 字符)")
    else:
        print(f"   ⚠️  LLM生成内容较少 ({len(llm_content) - len(template_content)} 字符)")
    
    # 检查特定标识
    if "{{" in llm_content:
        print("   ❌ LLM内容包含模板变量，可能未正确生成")
    else:
        print("   ✅ LLM内容不包含模板变量")
    
    # 保存对比文件
    (module_path / ".ac.mod.md.template").write_text(template_content, encoding='utf-8')
    (module_path / ".ac.mod.md.llm").write_text(llm_content, encoding='utf-8')
    
    print(f"💾 对比文件已保存:")
    print(f"   模板版本: {module_path}/.ac.mod.md.template")
    print(f"   LLM版本: {module_path}/.ac.mod.md.llm")

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("使用方法:")
        print("  python test_llm_generation.py <module_path>")
        print("\n示例:")
        print("  python test_llm_generation.py /path/to/module")
        print("\n说明:")
        print("  - 测试LLM文档生成功能是否正常工作")
        print("  - 对比模板生成和LLM生成的差异")
        sys.exit(1)
    
    module_path = sys.argv[1]
    
    # 测试LLM生成
    success = test_llm_generation(module_path)
    
    if success:
        # 对比模板和LLM生成
        test_template_vs_llm_comparison(module_path)
    
    print(f"\n{'='*60}")
    if success:
        print("✅ LLM文档生成测试完成!")
        print("\n💡 检查要点:")
        print("   1. LLM客户端是否可用")
        print("   2. 生成的内容是否比模板更丰富")
        print("   3. 内容是否不包含模板变量")
        print("   4. 对比两个版本的差异")
    else:
        print("❌ LLM文档生成测试失败!")
        print("\n🔧 可能的问题:")
        print("   1. API配置不正确")
        print("   2. 网络连接问题")
        print("   3. API密钥无效")
        print("   4. 模型名称错误")
        sys.exit(1)

if __name__ == "__main__":
    main()
