#!/usr/bin/env python3
"""
测试新的基于文件数量的模块发现逻辑
"""

import sys
from pathlib import Path
from generate_java_doc import find_java_modules

def test_module_discovery(project_path_str: str, max_files: int = 20):
    """
    测试新的模块发现逻辑
    
    Args:
        project_path_str: 项目路径字符串
        max_files: 每个模块的最大文件数量
    """
    project_path = Path(project_path_str).resolve()
    
    print(f"🔍 测试新的模块发现逻辑")
    print(f"📂 项目路径: {project_path}")
    print(f"📊 最大文件数阈值: {max_files}")
    print("=" * 60)
    
    if not project_path.exists():
        print(f"❌ 错误: 路径不存在 - {project_path}")
        return False
    
    if not project_path.is_dir():
        print(f"❌ 错误: 不是目录 - {project_path}")
        return False
    
    # 统计总的Java文件数量
    total_java_files = list(project_path.rglob("*.java"))
    print(f"📄 项目总Java文件数: {len(total_java_files)}")
    
    # 使用新的模块发现逻辑
    print(f"\n🔍 开始模块发现...")
    modules = find_java_modules(project_path, max_files_per_module=max_files)
    
    print(f"\n📦 发现 {len(modules)} 个模块:")
    print("=" * 60)
    
    total_files_in_modules = 0
    
    for i, module in enumerate(modules, 1):
        rel_path = module.relative_to(project_path) if module != project_path else Path(".")
        
        # 统计模块中的Java文件
        module_java_files = list(module.rglob("*.java"))
        java_count = len(module_java_files)
        total_files_in_modules += java_count
        
        # 计算路径深度
        depth = len(module.parts) - len(project_path.parts)
        indent = "  " * depth
        
        print(f"{indent}{i}. {rel_path}")
        print(f"{indent}   📄 Java文件: {java_count} 个")
        print(f"{indent}   📁 路径深度: {depth}")
        
        # 检查是否超过阈值
        if java_count > max_files:
            print(f"{indent}   ⚠️  超过阈值 ({java_count} > {max_files})")
        else:
            print(f"{indent}   ✅ 符合阈值 ({java_count} <= {max_files})")
        
        # 显示前几个Java文件
        if java_count > 0:
            print(f"{indent}   📝 文件示例:")
            for java_file in module_java_files[:3]:
                file_rel_path = java_file.relative_to(module)
                print(f"{indent}      - {file_rel_path}")
            if java_count > 3:
                print(f"{indent}      ... 还有 {java_count - 3} 个文件")
        
        print()
    
    # 统计信息
    print("=" * 60)
    print(f"📊 统计信息:")
    print(f"   总模块数: {len(modules)}")
    print(f"   总Java文件数: {len(total_java_files)}")
    print(f"   模块中文件数: {total_files_in_modules}")
    
    if total_files_in_modules == len(total_java_files):
        print(f"   ✅ 文件统计一致")
    else:
        print(f"   ⚠️  文件统计不一致 (差异: {abs(total_files_in_modules - len(total_java_files))})")
    
    # 检查模块大小分布
    module_sizes = [len(list(module.rglob("*.java"))) for module in modules]
    oversized_modules = [size for size in module_sizes if size > max_files]
    
    print(f"   超过阈值的模块: {len(oversized_modules)} 个")
    if oversized_modules:
        print(f"   超过阈值的模块大小: {oversized_modules}")
    
    avg_size = sum(module_sizes) / len(module_sizes) if module_sizes else 0
    print(f"   平均模块大小: {avg_size:.1f} 个文件")
    
    return len(oversized_modules) == 0

def test_different_thresholds(project_path_str: str):
    """测试不同阈值下的模块发现效果"""
    print(f"\n🔄 测试不同阈值的效果")
    print("=" * 60)
    
    project_path = Path(project_path_str).resolve()
    thresholds = [10, 20, 30, 50]
    
    for threshold in thresholds:
        print(f"\n📊 阈值: {threshold}")
        modules = find_java_modules(project_path, max_files_per_module=threshold)
        
        module_sizes = [len(list(module.rglob("*.java"))) for module in modules]
        oversized = [size for size in module_sizes if size > threshold]
        avg_size = sum(module_sizes) / len(module_sizes) if module_sizes else 0
        
        print(f"   模块数: {len(modules)}")
        print(f"   平均大小: {avg_size:.1f}")
        print(f"   超过阈值: {len(oversized)} 个")
        if oversized:
            print(f"   超过阈值的大小: {oversized}")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python test_new_module_discovery.py <project_path> [max_files]")
        print("\n示例:")
        print("  python test_new_module_discovery.py /path/to/java/project")
        print("  python test_new_module_discovery.py /path/to/java/project 15")
        print("\n说明:")
        print("  - 测试新的基于文件数量的模块发现逻辑")
        print("  - max_files: 每个模块的最大文件数量 (默认: 20)")
        sys.exit(1)
    
    project_path = sys.argv[1]
    max_files = int(sys.argv[2]) if len(sys.argv) > 2 else 20
    
    # 测试模块发现
    success = test_module_discovery(project_path, max_files)
    
    # 测试不同阈值
    test_different_thresholds(project_path)
    
    print(f"\n{'='*60}")
    if success:
        print("✅ 模块发现测试完成! 所有模块都符合大小要求")
        print("\n💡 新逻辑的优势:")
        print("   1. 不依赖构建文件 (pom.xml, build.gradle)")
        print("   2. 基于文件数量自动分层")
        print("   3. 确保每个模块大小合理")
        print("   4. 递归处理复杂的目录结构")
    else:
        print("⚠️  模块发现测试完成，但有些模块仍然超过阈值")
        print("\n🔧 可能需要:")
        print("   1. 调整阈值参数")
        print("   2. 检查目录结构是否合理")
        print("   3. 考虑手动拆分大型模块")

if __name__ == "__main__":
    main()
