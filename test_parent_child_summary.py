#!/usr/bin/env python3
"""
测试父模块总结子模块文档的功能
"""

import sys
from pathlib import Path
from generator import ACModGenerator
from llm_config import LLMConfig

def test_parent_child_summary(module_path_str: str):
    """
    测试父模块总结子模块文档的功能
    
    Args:
        module_path_str: 模块路径字符串
    """
    module_path = Path(module_path_str).resolve()
    
    print(f"🔍 测试父子模块文档总结功能")
    print(f"📂 模块路径: {module_path}")
    
    if not module_path.exists():
        print(f"❌ 错误: 路径不存在 - {module_path}")
        return False
    
    # 创建生成器
    generator = ACModGenerator(root_path=module_path.parent)
    
    # 创建LLM配置
    llm_config = LLMConfig(
        api_base="http://10.55.57.164:30804/qwen3-235b-fp8-openai-server/v1/v1",
        api_key="10298467",
        model="/Qwen2-235B-A22B-FP8"
    )
    generator.set_llm_config(llm_config)
    
    print(f"\n🚀 开始分层文档生成...")
    
    # 执行分层文档生成
    results = generator.generate_hierarchical_documentation(
        module_path=module_path,
        force=True,  # 强制覆盖现有文档
        enable_context_limiting=True
    )
    
    print(f"\n📊 生成结果:")
    success_count = sum(1 for success in results.values() if success)
    total_count = len(results)
    print(f"   成功: {success_count}/{total_count}")
    
    # 显示生成的文档
    print(f"\n📄 生成的文档文件:")
    main_doc = None
    child_docs = []
    
    for doc_path, success in results.items():
        status = "✅" if success else "❌"
        path_obj = Path(doc_path)
        
        if path_obj == module_path:
            main_doc = path_obj / ".ac.mod.md"
            print(f"   {status} 主模块: {main_doc}")
        else:
            child_doc = path_obj / ".ac.mod.md"
            rel_path = path_obj.relative_to(module_path)
            child_docs.append((rel_path, child_doc))
            print(f"   {status} 子模块: {rel_path}/.ac.mod.md")
    
    # 检查主模块文档是否包含子模块摘要
    if main_doc and main_doc.exists():
        print(f"\n🔍 检查主模块文档内容...")
        try:
            content = main_doc.read_text(encoding='utf-8')
            
            # 检查是否包含子模块概览部分
            if "## 子模块概览" in content:
                print("   ✅ 主模块文档包含子模块概览部分")
                
                # 统计提到的子模块数量
                child_count = content.count("### ")
                if "子模块概览" in content:
                    child_count -= 1  # 减去"子模块概览"标题本身
                
                print(f"   📊 文档中包含 {child_count} 个子模块的摘要")
                
                # 检查是否包含文档链接
                if ".ac.mod.md" in content:
                    print("   ✅ 包含子模块文档链接")
                else:
                    print("   ⚠️  未找到子模块文档链接")
                    
            else:
                print("   ❌ 主模块文档不包含子模块概览部分")
                
        except Exception as e:
            print(f"   ❌ 读取主模块文档失败: {e}")
    else:
        print(f"   ❌ 主模块文档不存在: {main_doc}")
    
    # 检查子模块文档
    print(f"\n📋 子模块文档检查:")
    for rel_path, child_doc in child_docs:
        if child_doc.exists():
            print(f"   ✅ {rel_path}/.ac.mod.md 存在")
        else:
            print(f"   ❌ {rel_path}/.ac.mod.md 不存在")
    
    return success_count > 0

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("使用方法:")
        print("  python test_parent_child_summary.py <java_module_path>")
        print("\n示例:")
        print("  python test_parent_child_summary.py /path/to/daip-patcher-service")
        print("\n说明:")
        print("  - 这个脚本会测试父模块总结子模块文档的功能")
        print("  - 先为所有子模块生成文档，然后生成包含子模块摘要的父模块文档")
        sys.exit(1)
    
    module_path = sys.argv[1]
    
    success = test_parent_child_summary(module_path)
    
    print(f"\n{'='*60}")
    if success:
        print("✅ 父子模块文档总结功能测试完成!")
        print("\n💡 检查要点:")
        print("   1. 主模块文档是否包含'子模块概览'部分")
        print("   2. 是否列出了所有子模块及其功能描述")
        print("   3. 是否包含指向子模块文档的链接")
        print("   4. 各个子模块是否都有独立的.ac.mod.md文件")
    else:
        print("❌ 父子模块文档总结功能测试失败!")
        sys.exit(1)

if __name__ == "__main__":
    main()
