#!/usr/bin/env python3
"""
测试子包发现功能的脚本
"""

import sys
from pathlib import Path
from generator import ACModGenerator
from module_scanner import ModuleScanner

def test_subpackage_discovery(module_path_str: str):
    """
    测试指定模块的子包发现功能
    
    Args:
        module_path_str: 模块路径字符串
    """
    module_path = Path(module_path_str).resolve()
    
    print(f"🔍 测试模块: {module_path}")
    print(f"📂 模块路径: {module_path}")
    
    if not module_path.exists():
        print(f"❌ 错误: 路径不存在 - {module_path}")
        return False
    
    # 创建模块扫描器
    scanner = ModuleScanner(module_path.parent)
    
    # 检查是否是Java包
    is_java_package = scanner._is_java_package(module_path)
    print(f"📦 是否为Java包: {is_java_package}")
    
    if not is_java_package:
        print("❌ 不是Java包，无法进行子包发现")
        return False
    
    # 统计Java文件
    java_files = list(module_path.rglob("*.java"))
    print(f"📄 Java文件总数: {len(java_files)}")
    
    # 发现子包
    print(f"\n🔍 开始发现子包...")
    subpackages = scanner._find_java_subpackages(module_path)
    
    print(f"✅ 发现 {len(subpackages)} 个子包:")
    
    if not subpackages:
        print("   (没有发现子包)")
        return True
    
    # 显示每个子包的详细信息
    for i, subpackage in enumerate(subpackages, 1):
        rel_path = subpackage.relative_to(module_path)
        java_files_in_pkg = list(subpackage.glob("*.java"))
        java_files_recursive = list(subpackage.rglob("*.java"))
        
        print(f"\n   {i}. {rel_path}")
        print(f"      📁 完整路径: {subpackage}")
        print(f"      📄 直接Java文件: {len(java_files_in_pkg)}")
        print(f"      📄 递归Java文件: {len(java_files_recursive)}")
        
        # 显示前几个Java文件
        if java_files_in_pkg:
            print(f"      📝 文件示例:")
            for java_file in java_files_in_pkg[:3]:
                print(f"         - {java_file.name}")
            if len(java_files_in_pkg) > 3:
                print(f"         ... 还有 {len(java_files_in_pkg) - 3} 个文件")
    
    return True

def test_hierarchical_generation(module_path_str: str):
    """
    测试分层文档生成
    
    Args:
        module_path_str: 模块路径字符串
    """
    module_path = Path(module_path_str).resolve()
    
    print(f"\n{'='*60}")
    print(f"🚀 测试分层文档生成")
    print(f"📂 模块: {module_path}")
    
    # 创建生成器
    generator = ACModGenerator(root_path=module_path.parent)
    
    # 执行分层文档生成（仅测试，不实际生成文件）
    print(f"\n📝 模拟分层文档生成...")
    
    # 先检查子包
    scanner = generator.scanner
    subpackages = scanner._find_java_subpackages(module_path)
    
    print(f"📊 预计生成文档:")
    print(f"   1. 主模块文档: {module_path}/.ac.mod.md")
    
    for i, subpackage in enumerate(subpackages, 2):
        rel_path = subpackage.relative_to(module_path)
        print(f"   {i}. 子包文档: {subpackage}/.ac.mod.md")
        print(f"      (相对路径: {rel_path}/.ac.mod.md)")
    
    total_docs = 1 + len(subpackages)
    print(f"\n📄 总计将生成: {total_docs} 个文档文件")
    
    return True

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("使用方法:")
        print("  python test_subpackage_discovery.py <java_module_path>")
        print("\n示例:")
        print("  python test_subpackage_discovery.py /path/to/daip-patcher-service")
        sys.exit(1)
    
    module_path = sys.argv[1]
    
    # 测试子包发现
    success1 = test_subpackage_discovery(module_path)
    
    if success1:
        # 测试分层文档生成
        success2 = test_hierarchical_generation(module_path)
    
    print(f"\n{'='*60}")
    if success1:
        print("✅ 子包发现测试完成")
        print("\n💡 提示:")
        print("   - 如果发现的子包数量符合预期，可以运行实际的文档生成")
        print("   - 使用 generate_java_doc.py 脚本进行实际文档生成")
    else:
        print("❌ 子包发现测试失败")

if __name__ == "__main__":
    main()
